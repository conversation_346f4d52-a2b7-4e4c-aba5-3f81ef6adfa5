/**
 * Unified timezone utility for consistent timestamp formatting across the application
 *
 * Format Requirements:
 * - Full DateTime: DD MMM YYYY, HH:mm AM/PM (Time Zone) - Example: 06 Oct 2025, 02:45 PM GMT+1
 * - Date Only: DD MMM YYYY - Example: 06 Oct 2025
 * - Time Only: HH:mm AM/PM (Time Zone) - Example: 02:45 PM GMT+1
 *
 * All timestamps from APIs are in UTC and need to be converted to user's timezone
 * Note: API timestamps without 'Z' suffix are automatically treated as UTC
 */

export type TimestampFormat = 'full' | 'date' | 'time';

/**
 * Mapping of IANA timezone identifiers to their abbreviations
 * This covers the most common timezones used globally
 */
const TIMEZONE_ABBREVIATIONS: Record<string, string> = {
  // GMT/UTC
  UTC: 'UTC',
  GMT: 'GMT',

  // Europe
  'Europe/London': 'GMT',
  'Europe/Dublin': 'GMT',
  'Europe/Berlin': 'CET',
  'Europe/Paris': 'CET',
  'Europe/Rome': 'CET',
  'Europe/Madrid': 'CET',
  'Europe/Amsterdam': 'CET',
  'Europe/Brussels': 'CET',
  'Europe/Vienna': 'CET',
  'Europe/Prague': 'CET',
  'Europe/Warsaw': 'CET',
  'Europe/Stockholm': 'CET',
  'Europe/Oslo': 'CET',
  'Europe/Copenhagen': 'CET',
  'Europe/Helsinki': 'EET',
  'Europe/Athens': 'EET',
  'Europe/Istanbul': 'TRT',
  'Europe/Moscow': 'MSK',

  // North America
  'America/New_York': 'EST',
  'America/Chicago': 'CST',
  'America/Denver': 'MST',
  'America/Los_Angeles': 'PST',
  'America/Vancouver': 'PST',
  'America/Toronto': 'EST',
  'America/Montreal': 'EST',
  'America/Mexico_City': 'CST',

  // Africa
  'Africa/Lagos': 'WAT',
  'Africa/Accra': 'GMT',
  'Africa/Cairo': 'EET',
  'Africa/Johannesburg': 'SAST',
  'Africa/Nairobi': 'EAT',
  'Africa/Casablanca': 'WET',

  // Asia
  'Asia/Tokyo': 'JST',
  'Asia/Shanghai': 'CST',
  'Asia/Hong_Kong': 'HKT',
  'Asia/Singapore': 'SGT',
  'Asia/Seoul': 'KST',
  'Asia/Kolkata': 'IST',
  'Asia/Dubai': 'GST',
  'Asia/Bangkok': 'ICT',
  'Asia/Jakarta': 'WIB',
  'Asia/Manila': 'PHT',

  // Australia/Oceania
  'Australia/Sydney': 'AEDT',
  'Australia/Melbourne': 'AEDT',
  'Australia/Brisbane': 'AEST',
  'Australia/Perth': 'AWST',
  'Pacific/Auckland': 'NZDT',

  // South America
  'America/Sao_Paulo': 'BRT',
  'America/Buenos_Aires': 'ART',
  'America/Lima': 'PET',
  'America/Bogota': 'COT',
};

/**
 * Get timezone abbreviation from IANA timezone identifier
 * Falls back to calculating GMT offset if timezone not in mapping
 */
function getTimezoneAbbreviation(timezone: string, date: Date): string {
  // Check if we have a predefined abbreviation
  if (TIMEZONE_ABBREVIATIONS[timezone]) {
    return TIMEZONE_ABBREVIATIONS[timezone];
  }

  // Fallback: calculate GMT offset
  try {
    const formatter = new Intl.DateTimeFormat('en', {
      timeZone: timezone,
      timeZoneName: 'short',
    });

    const parts = formatter.formatToParts(date);
    const timeZoneName = parts.find(
      part => part.type === 'timeZoneName'
    )?.value;

    if (timeZoneName && timeZoneName !== timezone) {
      return timeZoneName;
    }

    // Calculate offset manually
    const utcDate = new Date(date.toLocaleString('en-US', { timeZone: 'UTC' }));
    const localDate = new Date(
      date.toLocaleString('en-US', { timeZone: timezone })
    );
    const offsetMinutes =
      (localDate.getTime() - utcDate.getTime()) / (1000 * 60);
    const offsetHours = Math.floor(Math.abs(offsetMinutes) / 60);
    const offsetMins = Math.abs(offsetMinutes) % 60;
    const sign = offsetMinutes >= 0 ? '+' : '-';

    if (offsetMins === 0) {
      return `GMT${sign}${offsetHours}`;
    } else {
      return `GMT${sign}${offsetHours}:${offsetMins.toString().padStart(2, '0')}`;
    }
  } catch (error) {
    console.warn(`Failed to get timezone abbreviation for ${timezone}:`, error);
    return 'GMT';
  }
}

/**
 * Convert UTC timestamp to user's timezone and format according to specified format
 */
export function formatTimestamp(
  utcTimestamp: string | Date,
  userTimezone: string,
  format: TimestampFormat = 'full'
): string {
  try {
    // Parse the UTC timestamp - ensure proper UTC parsing
    let date: Date;
    // console.log('utcTimestamp', utcTimestamp);
    if (typeof utcTimestamp === 'string') {
      // If the timestamp doesn't end with 'Z', it's likely a UTC timestamp without timezone indicator
      // Add 'Z' to ensure it's parsed as UTC, not local time
      const timestampStr = utcTimestamp.endsWith('Z')
        ? utcTimestamp
        : `${utcTimestamp}Z`;
      date = new Date(timestampStr);
    } else {
      date = utcTimestamp;
    }

    if (isNaN(date.getTime())) {
      console.warn('Invalid timestamp provided:', utcTimestamp);
      return 'Invalid Date';
    }

    // Convert to user's timezone
    const timezoneAbbr = getTimezoneAbbreviation(userTimezone, date);

    // Format according to requirements using the user's timezone
    const day = date.toLocaleString('en-US', {
      timeZone: userTimezone,
      day: '2-digit',
    });
    const month = date.toLocaleString('en-US', {
      timeZone: userTimezone,
      month: 'short',
    });
    const year = date.toLocaleString('en-US', {
      timeZone: userTimezone,
      year: 'numeric',
    });

    // Use 12-hour format with AM/PM
    const timeFormatted = date.toLocaleString('en-US', {
      timeZone: userTimezone,
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
    });

    // console.log('timeFormatted', timeFormatted);

    switch (format) {
      case 'date':
        return `${day} ${month} ${year}`;
      case 'time':
        return `${timeFormatted} ${timezoneAbbr}`;
      case 'full':
      default:
        return `${day} ${month} ${year}, ${timeFormatted} ${timezoneAbbr}`;
    }
  } catch (error) {
    console.error('Error formatting timestamp:', error);
    return 'Invalid Date';
  }
}

/**
 * Utility function to check if a timezone is valid
 */
export function isValidTimezone(timezone: string): boolean {
  try {
    Intl.DateTimeFormat(undefined, { timeZone: timezone });
    return true;
  } catch {
    return false;
  }
}

/**
 * Get current timestamp in user's timezone with specified format
 */
export function getCurrentTimestamp(
  userTimezone: string,
  format: TimestampFormat = 'full'
): string {
  return formatTimestamp(new Date(), userTimezone, format);
}
