import clsx from 'clsx';
import { motion } from 'framer-motion';
import { ChevronDown, ChevronUp } from 'lucide-react';
import React, { useEffect, useRef, useState } from 'react';

export interface DropdownOption {
  id: string;
  name: string;
  icon: string;
}

interface AgentsDropdownProps {
  isOpen: boolean;
  onToggle: () => void;
  currentItem?: DropdownOption;
  options: DropdownOption[];
  onItemSelect: (option: DropdownOption) => void;
  placeholder: string;
  noOptionsMessage: string;
  onImageError?: (event: React.SyntheticEvent<HTMLImageElement, Event>) => void;
  getOptionImageError?: (
    option: DropdownOption
  ) => (event: React.SyntheticEvent<HTMLImageElement, Event>) => void;
  className?: string;
}

const AgentsDropdown: React.FC<AgentsDropdownProps> = ({
  isOpen,
  onToggle,
  currentItem,
  options,
  onItemSelect,
  placeholder,
  noOptionsMessage,
  onImageError,
  getOptionImageError,
  className,
}) => {
  const [alignRight, setAlignRight] = useState(false);
  const containerRef = useRef<HTMLDivElement | null>(null);
  const dropdownRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    if (!isOpen || !containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const centerX = rect.left + rect.width / 2;

    // If the trigger is in the right half of the viewport, align the menu to the right.
    setAlignRight(centerX > viewportWidth / 2);
  }, [isOpen]);

  // Close dropdown when clicking outside
  useEffect(() => {
    if (!isOpen) return;

    const handleClickOutside = (event: MouseEvent) => {
      const container = containerRef.current;
      if (!container) return;

      const triggerClicked = container.contains(event.target as Node);
      const dropdownClicked = dropdownRef.current?.contains(
        event.target as Node
      );

      if (!triggerClicked && !dropdownClicked) {
        onToggle();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onToggle]);

  return (
    <div className="relative" ref={containerRef}>
      <button
        onClick={onToggle}
        type="button"
        className={clsx(
          'flex h-[48px] items-center gap-3 rounded-[10px] border border-gray-200 bg-white p-2 md:w-fit',
          className
        )}
      >
        <div className="flex w-full items-center gap-3">
          {currentItem && currentItem.icon ? (
            <img
              src={currentItem?.icon || ''}
              alt={currentItem?.name || placeholder}
              className="h-6 w-6 rounded-lg bg-peachTwo object-cover sm:h-8 sm:w-8"
              onError={onImageError}
            />
          ) : (
            <div className="h-8 w-8 rounded-lg bg-peachTwo" />
          )}
          <span className="text-sm font-normal text-blackOne">
            {currentItem?.name || placeholder}
          </span>
        </div>
        {isOpen ? (
          <ChevronUp className="h-4 w-4 text-gray-500" />
        ) : (
          <ChevronDown className="h-4 w-4 text-gray-500" />
        )}
      </button>

      {isOpen && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          className={clsx(
            'absolute top-full z-50 mt-1 w-full min-w-40 rounded-xl border border-gray-200 bg-white shadow-[0px_4px_12px_0px_#8890A133]',
            alignRight ? 'left-auto right-0' : 'left-0 right-auto'
          )}
          ref={dropdownRef}
        >
          <div className="flex w-full flex-col divide-y divide-gray-200">
            {options.length &&
            options.filter(option => option.id !== currentItem?.id).length >
              0 ? (
              options
                .filter(option => option.id !== currentItem?.id)
                .map(option => (
                  <button
                    key={option.id}
                    onMouseDown={e => {
                      e.preventDefault();
                      e.stopPropagation();
                      onItemSelect(option);
                    }}
                    className="flex w-full items-center gap-2 p-2 text-left text-sm font-medium transition-colors first:rounded-t-xl last:rounded-b-xl hover:bg-gray-50 sm:text-base"
                  >
                    <img
                      src={option.icon}
                      alt={option.name}
                      className="h-6 w-6 rounded-lg bg-peachTwo object-cover sm:h-8 sm:w-8"
                      onError={getOptionImageError?.(option)}
                    />
                    {option.name}
                  </button>
                ))
            ) : (
              <button className="flex h-10 w-full items-center gap-2 rounded-xl p-4 text-left text-sm font-normal transition-colors hover:bg-gray-50">
                {noOptionsMessage}
              </button>
            )}
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default AgentsDropdown;
