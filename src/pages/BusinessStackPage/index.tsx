import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useLocation, useNavigate, useParams } from 'react-router-dom';

import { SalesforceConnectionModal } from '@/components/businessStack/SalesforceConnectionModal';
import { TwilioConnectionModal } from '@/components/businessStack/TwilioConnectionModal';
import AgentsDropdown from '@/components/ui/AgentsDropdown';
import { useTenant } from '@/context/TenantContext';
import { useAvailableAppsQuery } from '@/hooks/useBusinessStackQueries';
import { useOnClickOutside } from '@/hooks/useOnClickOutside';
import { useGetUserProfile } from '@/hooks/useUserProfile';
import { UserBasicInfoPayload } from '@/types/user';
import {
  formatAppNameFromKey,
  hasOAuthError,
} from '@/utils/oauthRedirectHandler';

import { Icons } from '../../assets/icons/DashboardIcons';
import { AvailableAppsGrid } from '../../components/businessStack/AvailableAppsGrid';
import OAuthErrorModal from '../../components/businessStack/OAuthErrorModal';
import AppContainer from '../../components/common/AppContainer';
import EnhancedChatSidebar from '../../components/common/EnhancedChatSidebar';
import { useConnectionFlow } from '../../hooks/useConnectionFlow';
import { BusinessStackPageState } from '../../types/businessStack';

const BusinessStackPage: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const params = useParams<{ appKey?: string }>();

  // Get modal state from URL params
  const searchParams = new URLSearchParams(location.search);
  const modalParam = searchParams.get('modal');
  const [pageState, setPageState] = useState<BusinessStackPageState>({
    availableApps: [],
    isLoadingApps: false,
    searchQuery: '',
    selectedCategory: '',
    scyraChatState: {
      messages: [],
      isLoading: false,
      sessionId: '',
    },
    currentPage: 1,
    totalPages: 1,
  });

  const [selectedSuite, setSelectedSuite] = useState<string>('');
  const [selectedAgent, setSelectedAgent] = useState<string>('');
  const [isSuiteDropdownOpen, setIsSuiteDropdownOpen] = useState(false);
  const [isAgentDropdownOpen, setIsAgentDropdownOpen] = useState(false);
  const suiteDropdownRef = useRef<HTMLDivElement>(null);
  const agentDropdownRef = useRef<HTMLDivElement>(null);
  useOnClickOutside(suiteDropdownRef, () => setIsSuiteDropdownOpen(false));
  useOnClickOutside(agentDropdownRef, () => setIsAgentDropdownOpen(false));

  const [isProcessingOAuth, setIsProcessingOAuth] = useState(false);
  const reloadChatHistoryRef = useRef<(() => Promise<void>) | null>(null);
  const oauthCallbackProcessedRef = useRef<string | null>(null); // Track processed OAuth callbacks

  // Initialize connection flow
  const connectionFlow = useConnectionFlow((app, isConnected) => {
    setPageState(prev => ({
      ...prev,
      availableApps: prev.availableApps.map(existingApp =>
        existingApp.key === app.key
          ? { ...existingApp, isConnected }
          : existingApp
      ),
    }));
  });

  const [oauthErrorModal, setOAuthErrorModal] = useState({
    isOpen: false,
    appName: '',
  });

  const { data: userData } = useGetUserProfile<UserBasicInfoPayload>();
  const {
    activeAgent,
    setActiveAgent,
    isLoading: isActiveTenantLoading,
  } = useTenant();

  // Get suite options from user data
  // Safely extract suiteOptions and currentSuiteAgents, handling missing/undefined data
  const claimedAgentSuites = useMemo(
    () => userData?.userInfo?.tenant?.claimedAgentSuites ?? [],
    [userData]
  );

  const suiteOptions = useMemo(
    () =>
      claimedAgentSuites.map(suite => ({
        id: suite?.suite?.agentSuiteKey ?? '',
        name: suite?.suite?.agentSuiteName ?? '',
        icon: suite?.suite?.avatar ?? '',
        fileData: suite?.suite?.fileData ?? null,
      })),
    [claimedAgentSuites]
  );

  // Get agents only from the currently selected suite
  const currentSuiteAgents = useMemo(
    () =>
      claimedAgentSuites
        .find(suite => suite?.suite?.agentSuiteKey === selectedSuite)
        ?.suite?.availableAgents?.slice()
        .sort((a, b) =>
          (a.agentName || '').localeCompare(b.agentName || '', undefined, {
            sensitivity: 'base',
          })
        )
        .map(agent => ({
          ...agent,
          suiteKey: selectedSuite,
        })) ?? [],
    [claimedAgentSuites, selectedSuite]
  );

  // Initialize suite selection
  useEffect(() => {
    if (suiteOptions.length > 0 && !selectedSuite) {
      // If there's an active agent, find the suite it belongs to
      if (activeAgent) {
        const suiteWithActiveAgent = claimedAgentSuites.find(suite =>
          suite?.suite?.availableAgents?.some(
            agent => agent.agentKey === activeAgent
          )
        );

        if (suiteWithActiveAgent) {
          setSelectedSuite(suiteWithActiveAgent.suite.agentSuiteKey);
        } else {
          // Fallback to first suite if active agent not found
          setSelectedSuite(suiteOptions[0].id);
        }
      } else {
        // No active agent, default to first suite
        setSelectedSuite(suiteOptions[0].id);
      }
    }
  }, [suiteOptions, selectedSuite, activeAgent, claimedAgentSuites]);

  // Ensure an agent is always selected when suite changes or agents are available
  useEffect(() => {
    if (currentSuiteAgents.length > 0) {
      // Check if current selected agent exists in the current suite
      const agentExistsInSuite = currentSuiteAgents.some(
        agent => agent.agentKey === selectedAgent
      );

      // If no agent is selected or the selected agent doesn't exist in current suite
      if (!selectedAgent || !agentExistsInSuite) {
        // Prefer activeAgent if it exists in current suite, otherwise use first agent
        const agentToSelect =
          currentSuiteAgents.find(agent => agent.agentKey === activeAgent)
            ?.agentKey || currentSuiteAgents[0].agentKey;
        setSelectedAgent(agentToSelect);
        setActiveAgent(agentToSelect);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentSuiteAgents, selectedSuite]);

  const handleAgentChange = (agentKey: string) => {
    setSelectedAgent(agentKey);
    setActiveAgent(agentKey);
    setIsAgentDropdownOpen(false);
  };

  // Use React Query to fetch available apps with caching
  const {
    data: appsData,
    isLoading: isLoadingApps,
    refetch: refetchApps,
  } = useAvailableAppsQuery({
    page: pageState.currentPage,
    size: 10,
    search: pageState.searchQuery,
    appCategory: pageState.selectedCategory,
  });

  // Update page state when apps data changes
  useEffect(() => {
    if (appsData?.data) {
      setPageState(prev => ({
        ...prev,
        availableApps: appsData.data.availableApps,
        totalPages: appsData.data.total,
      }));
    }
  }, [appsData]);

  // Reset connection flow when agent changes (chat history is handled by useScyraChat)
  useEffect(() => {
    if (isActiveTenantLoading || !activeAgent) return;

    // Reset connection flow state completely (don't preserve messages from other agents)
    connectionFlow.resetFlow(false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeAgent, isActiveTenantLoading]);

  // Handle OAuth callback parameters on component mount
  useEffect(() => {
    const handleOAuthCallback = async () => {
      const urlParams = new URLSearchParams(location.search);
      const appKey = params.appKey; // Get appKey from path parameter
      const code = urlParams.get('code');
      const state = urlParams.get('state');

      // Check for OAuth errors first
      if (appKey && hasOAuthError()) {
        const appName = formatAppNameFromKey(appKey);
        setOAuthErrorModal({
          isOpen: true,
          appName,
        });

        // Clean up URL - navigate back to business-stack without appKey
        navigate('/dashboard/business-stack', { replace: true });
        return;
      }

      // Only process if we have both appKey and code (OAuth callback scenario)
      if (appKey && code) {
        // Create unique callback identifier to prevent duplicate processing
        const callbackId = `${appKey}-${code}-${state}`;

        // Check if this callback has already been processed
        if (oauthCallbackProcessedRef.current === callbackId) {
          // console.log(
          //   'OAuth callback already processed, skipping:',
          //   callbackId
          // );
          return;
        }

        // Check if already processing OAuth to prevent concurrent processing
        if (isProcessingOAuth) {
          // console.log('OAuth callback already in progress, skipping');
          return;
        }

        // console.log('Processing OAuth callback for:', callbackId);
        oauthCallbackProcessedRef.current = callbackId;
        setIsProcessingOAuth(true);

        // Small delay to ensure all state updates are complete
        await new Promise(resolve => setTimeout(resolve, 100));

        try {
          // Find the app by key from cached data or refetch if needed
          let targetApp = appsData?.data.availableApps.find(
            app => app.key === appKey
          );

          // If not found in current data, refetch all apps
          if (!targetApp) {
            const { data: freshAppsData } = await refetchApps();
            targetApp = freshAppsData?.data.availableApps.find(
              app => app.key === appKey
            );
          }

          if (targetApp) {
            // console.log('Found target app for OAuth callback:', targetApp.name);
            // Process the OAuth callback with the full app object
            // DO NOT call startConnection here as it would trigger another OAuth flow
            await connectionFlow.handleOAuthCallbackWithApp(
              targetApp,
              code,
              state || undefined
            );
            // console.log(
            //   'OAuth callback processing completed for:',
            //   targetApp.name
            // );
          } else {
            console.error('App not found for key:', appKey);
            throw new Error(`App not found for key: ${appKey}`);
          }
        } catch (error) {
          console.error('Error processing OAuth callback:', error);
          // Reset the processed flag on error so it can be retried
          oauthCallbackProcessedRef.current = null;
        } finally {
          setIsProcessingOAuth(false);

          // Clean up URL - navigate back to business-stack without appKey
          navigate('/dashboard/business-stack', { replace: true });
        }
      } // Close the if (appKey && code) block
    };

    handleOAuthCallback();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    location.search,
    params.appKey,
    isProcessingOAuth,
    appsData,
    refetchApps,
  ]); // Include isProcessingOAuth to prevent concurrent processing

  // Handle search query changes
  const handleSearchChange = (query: string) => {
    setPageState(prev => ({
      ...prev,
      searchQuery: query,
      currentPage: 1, // Reset to first page when searching
    }));
  };

  // Handle category changes
  const handleCategoryChange = (category: string) => {
    setPageState(prev => ({
      ...prev,
      selectedCategory: category,
      currentPage: 1, // Reset to first page when filtering
    }));
  };

  const handleOpenTwilioModal = () => {
    navigate('?modal=twilio', { replace: true });
  };

  const handleCloseTwilioModal = () => {
    navigate('/dashboard/business-stack', { replace: true });
  };

  const handleTwilioConnectionSuccess = () => {
    setPageState(prev => ({
      ...prev,
      availableApps: prev.availableApps.map(app =>
        app.name === 'Twilio' ? { ...app, isConnected: true } : app
      ),
    }));
    // Refetch apps to get updated connection status
    refetchApps();
  };

  const handleOpenSalesforceModal = () => {
    navigate('?modal=salesforce', { replace: true });
  };

  const handleCloseSalesforceModal = () => {
    navigate('/dashboard/business-stack', { replace: true });
  };

  const handleSalesforceConnectionSuccess = () => {
    setPageState(prev => ({
      ...prev,
      availableApps: prev.availableApps.map(app =>
        app.name === 'Salesforce' ? { ...app, isConnected: true } : app
      ),
    }));
    // Refetch apps to get updated connection status
    refetchApps();
  };

  const handleCloseOAuthErrorModal = () => {
    setOAuthErrorModal({
      isOpen: false,
      appName: '',
    });
  };

  // Handle app connection (for non-modal apps)
  const handleConnectApp = async (appName: string) => {
    // This will be handled by the connection flow now
    console.log('Connecting to app:', appName);
  };

  // Show loading state while processing OAuth callback
  if (isProcessingOAuth) {
    return (
      <div className="flex h-full flex-col">
        <div className="flex flex-1 items-center justify-center">
          <div className="flex flex-col items-center justify-center">
            <div className="mb-4 h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
            <p className="text-blackFour">Processing authentication...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-full flex-col">
      <div className="flex flex-1 overflow-hidden">
        <div className="flex flex-1 overflow-hidden">
          {/* LHS - Chat Interface */}
          <EnhancedChatSidebar
            connectionFlow={connectionFlow}
            reloadChatHistoryRef={reloadChatHistoryRef}
          />

          {/* RHS - Available Apps Grid */}
          <div className="flex-1 overflow-y-auto">
            <AppContainer className="space-y-6 p-8 lg:space-y-8">
              <div className="lg:max-w-[800px]">
                <div className="mb-6">
                  <div className="flex w-full items-center justify-between gap-12">
                    <div className="flex items-center gap-3">
                      <Icons.Stack className="h-6 w-6 text-primary" />
                      <h1 className="text-xl font-semibold text-blackFour lg:text-2xl">
                        Business Stack
                      </h1>
                    </div>

                    {/* AgentsDropdown Component */}
                    <div className="flex items-center gap-8">
                      <div className="relative" ref={suiteDropdownRef}>
                        <AgentsDropdown
                          isOpen={isSuiteDropdownOpen}
                          onToggle={() =>
                            setIsSuiteDropdownOpen(!isSuiteDropdownOpen)
                          }
                          currentItem={suiteOptions.find(
                            s => s.id === selectedSuite
                          )}
                          options={suiteOptions}
                          onItemSelect={suite => {
                            setSelectedSuite(suite.id);
                            setIsSuiteDropdownOpen(false);
                          }}
                          placeholder="Suite"
                          noOptionsMessage="No suites"
                        />
                      </div>

                      <div className="relative" ref={agentDropdownRef}>
                        <AgentsDropdown
                          isOpen={isAgentDropdownOpen}
                          onToggle={() =>
                            setIsAgentDropdownOpen(!isAgentDropdownOpen)
                          }
                          currentItem={currentSuiteAgents
                            .map(a => ({
                              id: a.agentKey,
                              name: a.agentName,
                              icon: a.avatar,
                            }))
                            .find(a => a.id === selectedAgent)}
                          options={currentSuiteAgents.map(a => ({
                            id: a.agentKey,
                            name: a.agentName,
                            icon: a.avatar,
                          }))}
                          onItemSelect={agent => handleAgentChange(agent.id)}
                          placeholder="Agent"
                          noOptionsMessage="No agents"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex-1 overflow-hidden">
                  <AvailableAppsGrid
                    apps={pageState.availableApps}
                    isLoading={isLoadingApps}
                    searchQuery={pageState.searchQuery}
                    selectedCategory={pageState.selectedCategory}
                    onSearchChange={handleSearchChange}
                    onCategoryChange={handleCategoryChange}
                    onConnectApp={handleConnectApp}
                    connectionFlow={connectionFlow}
                    onOpenTwilioModal={handleOpenTwilioModal}
                    onOpenSalesforceModal={handleOpenSalesforceModal}
                  />
                </div>
              </div>
            </AppContainer>
          </div>
        </div>
        <TwilioConnectionModal
          isOpen={modalParam === 'twilio'}
          onClose={handleCloseTwilioModal}
          onConnectionSuccess={handleTwilioConnectionSuccess}
        />
        <SalesforceConnectionModal
          isOpen={modalParam === 'salesforce'}
          onClose={handleCloseSalesforceModal}
          onConnectionSuccess={handleSalesforceConnectionSuccess}
        />
        <OAuthErrorModal
          isOpen={oauthErrorModal.isOpen}
          onClose={handleCloseOAuthErrorModal}
          appName={oauthErrorModal.appName}
        />
      </div>
    </div>
  );
};

export default BusinessStackPage;
