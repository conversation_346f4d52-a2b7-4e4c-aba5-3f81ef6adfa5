import { ArrowLeft } from 'lucide-react';
import React, { useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';

import { Icons } from '@/assets/icons/DashboardIcons';
import { businessStackBg } from '@/assets/images';
import EnhancedChatSidebar from '@/components/common/EnhancedChatSidebar';
import { NotificationContainer } from '@/components/ui';
import { useTenant } from '@/context/TenantContext';
import { useHeartbeat } from '@/hooks/useHeartbeat';
import { useNotifications } from '@/hooks/useNotifications';
import { useGetUserProfile } from '@/hooks/useUserProfile';
import { AgentCard } from '@/pages/AiAgentsPage';

import AgentSelectionLayout from '../../../components/layout/AgentSelectionLayout';
import { ROUTES } from '../../../constants/routes';

type SelectStep = 'suite' | 'agent';

const title = 'Seamless System Connections';
const description =
  'Link agents to your CRM, communication tools, and workflows with secure per-agent authentication.';

const BusinessStackSelectAgentPage: React.FC = () => {
  const navigate = useNavigate();
  const [step, setStep] = useState<SelectStep>('suite');
  const [availableAgents, setAvailableAgents] = useState<any[]>([]);
  const { activeAgent, setActiveAgent } = useTenant();

  const { data: userData } = useGetUserProfile();

  const { notifications, dismiss } = useNotifications();
  const reloadChatHistoryRef = useRef<(() => Promise<void>) | null>(null);

  // Heartbeat functionality
  const {
    heartbeatState,
    fetchHeartbeats,
    initializeHeartbeat,
    pauseHeartbeat,
    getHeartbeatStatus,
  } = useHeartbeat();

  // Fetch heartbeat data when component mounts
  useEffect(() => {
    fetchHeartbeats();
  }, [fetchHeartbeats]);

  // Handle heartbeat actions
  const handleHeartbeatAction = async (
    action: 'initialize' | 'pause',
    agentKey: string
  ): Promise<void> => {
    try {
      if (action === 'initialize') {
        await initializeHeartbeat(agentKey);
      } else {
        await pauseHeartbeat(agentKey);
      }
    } catch (error) {
      // Error handling is done in the useHeartbeat hook
      console.error('Heartbeat action failed:', error);
    }
  };

  // Check if user has claimed the specific agents suite
  const isAgentSuiteClaimed = (suiteKey: string) => {
    return userData?.userInfo?.tenant?.claimedAgentSuites?.some(
      claimedSuite => claimedSuite.suite.agentSuiteKey === suiteKey
    );
  };

  const handleAgentSelect = (agentKey: string) => {
    setActiveAgent(agentKey);
    navigate(ROUTES.DASHBOARD_BUSINESS_STACK);
  };

  const handleBack = () => {
    setStep('suite');
  };

  return (
    <div className="h-full">
      {step === 'suite' ? (
        <AgentSelectionLayout
          title={title}
          description={description}
          bgImage={businessStackBg}
          pageType="business-stack"
          onAgentSuiteClick={suite => {
            if (!isAgentSuiteClaimed(suite.agentSuiteKey)) {
              navigate(
                ROUTES.DASHBOARD_BUSINESS_STACK_ACTIVATE_SUITE(
                  suite.agentSuiteKey
                )
              );
            } else {
              setAvailableAgents(
                [...(suite.availableAgents || [])].sort((a, b) =>
                  (a.agentName || '').localeCompare(
                    b.agentName || '',
                    undefined,
                    {
                      sensitivity: 'base',
                    }
                  )
                )
              );
              setStep('agent');
            }
          }}
        />
      ) : (
        <div className="flex h-full">
          {/* Chat Interface - LHS */}
          <EnhancedChatSidebar reloadChatHistoryRef={reloadChatHistoryRef} />

          {/* Main Content - RHS */}
          <div className="flex flex-1 flex-col gap-6 overflow-y-auto p-8">
            <div className="flex flex-col gap-4 text-start">
              <div className="mb-2 flex items-center space-x-4 hover:text-primary">
                <button
                  onClick={handleBack}
                  className="flex h-8 w-8 items-center justify-center rounded-full hover:bg-gray-100"
                >
                  <ArrowLeft className="h-4 w-4" />
                </button>

                <Icons.Stack className="h-6 w-6 text-primary" />

                <h1 className="text-2xl font-semibold text-blackOne">
                  Business Stack
                </h1>
              </div>

              {/* Hero Section */}
              <div
                className="flex h-[140px] max-w-4xl items-center overflow-hidden rounded-2xl bg-cover bg-center text-white"
                style={{
                  backgroundImage: `url(${businessStackBg})`,
                }}
              >
                <div className="flex w-full items-center justify-between">
                  <div className="p-6 text-left">
                    <h2 className="mb-2 text-lg font-bold text-white">
                      {title}
                    </h2>
                    <p className="text-sm text-white">{description}</p>
                  </div>
                </div>
                <div className="relative mr-8 h-full w-[178px]">
                  <img
                    src={businessStackBg}
                    alt="bg"
                    className="h-full w-full"
                  />
                </div>
              </div>
            </div>

            <h3 className="text-lg font-medium text-blackOne">
              {`Select Agent -> Connect Business Systems`}
            </h3>

            {/* Content */}
            <div className="w-fit">
              {/* Notifications Container */}
              <NotificationContainer
                notifications={notifications}
                onClose={dismiss}
                className="z-50 mb-8"
                maxNotifications={3}
              />
              <div className="grid w-full flex-1 grid-cols-1 gap-6 md:grid-cols-2">
                {availableAgents.map(agent => (
                  <AgentCard
                    key={agent.agentKey}
                    agent={agent}
                    className="w-[354px]"
                    isActiveAgent={activeAgent === agent.agentKey}
                    link="#"
                    onAgentSelect={() => handleAgentSelect(agent.agentKey)}
                    showHeartbeatControl
                    heartbeatStatus={getHeartbeatStatus(agent.agentKey)}
                    onHeartbeatAction={handleHeartbeatAction}
                    isHeartbeatLoading={
                      heartbeatState.loadingAgent === agent.agentKey
                    }
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BusinessStackSelectAgentPage;
