import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';

import { Input } from '@/components/ui';
import { ROUTES } from '@/constants/routes';
import { useNotifications } from '@/hooks/useNotifications';
import { useRequestTokenMutation } from '@/hooks/useUserProfile';

interface AccountInformationProps {
  email: string;
}

export const AccountInformation: React.FC<AccountInformationProps> = ({
  email,
}) => {
  const navigate = useNavigate();
  const requestTokenMutation = useRequestTokenMutation();
  const { notifyCustom } = useNotifications();
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  const [isChangingEmail, setIsChangingEmail] = useState(false);

  const handleChangePassword = async () => {
    setIsChangingPassword(true);
    try {
      // Request token before navigating
      await requestTokenMutation.mutateAsync();

      notifyCustom({
        message: 'Verification token sent to your email',
        type: 'success',
      });

      // Navigate to change password page after successful token request
      setTimeout(() => {
        navigate(ROUTES.SETTINGS_CHANGE_PASSWORD);
      }, 1500);
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : 'Failed to request verification token';
      notifyCustom({
        message: errorMessage,
        type: 'error',
      });
    } finally {
      setIsChangingPassword(false);
    }
  };

  const handleChangeEmail = async () => {
    setIsChangingEmail(true);
    try {
      // Request token before navigating
      await requestTokenMutation.mutateAsync();

      notifyCustom({
        message: 'Verification token sent to your email',
        type: 'success',
      });

      // Navigate to change email page after successful token request
      setTimeout(() => {
        navigate(ROUTES.SETTINGS_CHANGE_EMAIL);
      }, 1500);
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : 'Failed to request verification token';
      notifyCustom({
        message: errorMessage,
        type: 'error',
      });
    } finally {
      setIsChangingEmail(false);
    }
  };

  return (
    <div className="w-full max-w-2xl">
      <h2 className="mb-6 text-xl font-semibold text-blackOne">
        Account Information
      </h2>
      <div className="space-y-6">
        <div className="grid grid-cols-1 gap-x-8 gap-y-6 md:grid-cols-2">
          <div>
            <label className="mb-2 block text-[13px] text-subText">Email</label>
            <Input
              type="email"
              value={email}
              disabled
              className="h-10 w-full cursor-not-allowed rounded-md border border-[#DFEAF2] bg-white px-3 py-2 text-gray-500 focus:outline-none"
            />
            <button
              type="button"
              onClick={handleChangeEmail}
              disabled={isChangingEmail || isChangingPassword}
              className="mt-2 flex text-sm text-primary hover:underline disabled:cursor-not-allowed disabled:opacity-50"
            >
              {isChangingEmail ? 'Requesting...' : 'Change Email'}
            </button>
          </div>
          <div>
            <label className="mb-2 block text-[13px] text-subText">
              Password
            </label>
            <Input
              type="password"
              value="************"
              disabled
              className="h-10 w-full cursor-not-allowed rounded-md border border-[#DFEAF2] px-3 py-2 text-gray-500 focus:outline-none disabled:bg-white"
            />
            <button
              type="button"
              onClick={handleChangePassword}
              disabled={isChangingPassword || isChangingEmail}
              className="mt-2 flex text-sm text-primary hover:underline disabled:cursor-not-allowed disabled:opacity-50"
            >
              {isChangingPassword ? 'Requesting...' : 'Change Password'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
