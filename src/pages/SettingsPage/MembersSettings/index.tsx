import clsx from 'clsx';
import { Plus, Search } from 'lucide-react';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useLocation, useNavigate, useSearchParams } from 'react-router-dom';

import { DashboardWithChatLayout } from '@/components/layout/DashboardWithChatLayout';
import { Input, NotificationContainer } from '@/components/ui';
import AgentsDropdown, { DropdownOption } from '@/components/ui/AgentsDropdown';
import { ROUTES } from '@/constants/routes';
import { useTenant } from '@/context/TenantContext';
import { useGetAIAgentSuites } from '@/hooks/useAgents';
import { useSuiteInvites, useSuiteMembers } from '@/hooks/useMembers';
import { useNotifications } from '@/hooks/useNotifications';
import { useOnClickOutside } from '@/hooks/useOnClickOutside';
import { useSuiteJoinRequests } from '@/hooks/useRequestToJoin';
import { useGetUserProfile } from '@/hooks/useUserProfile';

import { Icons } from '../../../assets/icons/DashboardIcons';
import AnimatedTabs from '../../../components/ui/AnimatedTabs';
import { InvitesTab } from './InvitesTab';
import { MembersTab } from './MembersTab';
import { RequestsTab } from './RequestsTab';

const MembersSettings: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { setActiveAgent } = useTenant();
  const reloadChatHistoryRef = useRef<(() => Promise<void>) | null>(null);
  const desktopSuiteDropdownRef = useRef<HTMLDivElement>(null);
  const mobileSuiteDropdownRef = useRef<HTMLDivElement>(null);
  const [isSuiteMobileDropdownOpen, setIsSuiteMobileDropdownOpen] =
    useState<boolean>(false);
  // Use different refs for desktop and mobile to avoid conflicts
  useOnClickOutside(desktopSuiteDropdownRef, () => {
    // Only close if we're on desktop (window width >= 640px)
    if (window.innerWidth >= 640) {
      setIsSuiteDropdownOpen(false);
    }
  });

  useOnClickOutside(mobileSuiteDropdownRef, () => {
    // Only close if we're on mobile (window width < 640px)
    if (window.innerWidth < 640) {
      setIsSuiteMobileDropdownOpen(false);
    }
  });

  const [searchParams, setSearchParams] = useSearchParams();

  // Get agentSuiteKey from navigation state (passed when clicking "Add Members" from suite settings)
  const navigationState = location.state as {
    agentSuiteKey?: string;
    returnParams?: string;
  } | null;
  const [membersSearchQuery, setMembersSearchQuery] = useState<string>('');
  const [invitesSearchQuery, setInvitesSearchQuery] = useState<string>('');
  const [requestsSearchQuery, setRequestsSearchQuery] = useState<string>('');
  const [showMobileSearch] = useState<boolean>(false);

  const [isSuiteDropdownOpen, setIsSuiteDropdownOpen] = useState(false);
  const [currentSuite, setCurrentSuite] = useState<DropdownOption | undefined>(
    undefined
  );
  const { data: userData } = useGetUserProfile();
  const { data: allAgentSuites = [] } = useGetAIAgentSuites();
  const { notify, notifications, dismiss } = useNotifications();

  const suiteOptions = useMemo<DropdownOption[]>(() => {
    const claimedSuites = userData?.userInfo?.tenant?.claimedAgentSuites || [];

    // If user has claimed suites, use them
    if (claimedSuites.length > 0) {
      return claimedSuites.map(suite => ({
        id: suite.suite.agentSuiteKey,
        name: suite.suite.agentSuiteName,
        icon: suite.suite.avatar || '',
      }));
    }

    // Fallback to all available agent suites from API
    return allAgentSuites.map(suite => ({
      id: suite.agentSuiteKey,
      name: suite.agentSuiteName,
      icon: suite.avatar || '',
    }));
  }, [userData, allAgentSuites]);
  // Initialize suite selection - prefer suite from navigation state, otherwise use first suite
  useEffect(() => {
    if (suiteOptions.length > 0 && !currentSuite) {
      // If we have a suite key from navigation state (e.g., from "Add Members" button), use it
      if (navigationState?.agentSuiteKey) {
        const targetSuite = suiteOptions.find(
          suite => suite.id === navigationState.agentSuiteKey
        );
        if (targetSuite) {
          setCurrentSuite(targetSuite);
          return;
        }
      }
      // Otherwise, default to first suite
      setCurrentSuite(suiteOptions[0]);
    }
  }, [suiteOptions, currentSuite, navigationState?.agentSuiteKey]);

  // Set active agent to Regis when component mounts
  useEffect(() => {
    // Small delay to ensure TenantContext is fully initialized
    const timer = setTimeout(() => {
      setActiveAgent('regis');
    }, 100);

    return () => clearTimeout(timer);
  }, [setActiveAgent]);

  const agentSuiteKey = currentSuite?.id;

  const currentSuiteData = userData?.userInfo?.tenant?.claimedAgentSuites?.find(
    suite => suite.suite.agentSuiteKey === currentSuite?.id
  );

  const currentSuiteMember = currentSuiteData?.members?.find(
    member => member?.user?.userId === userData?.userInfo?.userId
  );

  const memberRoles = currentSuiteMember?.memberRoles || [];

  const isUserMember = Boolean(currentSuiteMember);
  const canManageMembers = memberRoles.includes('MANAGER');
  const canViewRequestsTab = isUserMember && canManageMembers;

  const allowedTabs = useMemo<('members' | 'invites' | 'requests')[]>(
    () =>
      canViewRequestsTab
        ? ['members', 'invites', 'requests']
        : ['members', 'invites'],
    [canViewRequestsTab]
  );

  const requestedTabParam =
    (searchParams.get('tab') as 'members' | 'invites' | 'requests' | null) ||
    'members';

  const activeTab = allowedTabs.includes(requestedTabParam)
    ? requestedTabParam
    : 'members';

  // Get counts from API endpoints
  const membersCountFilters = useMemo(
    () => ({
      agentSuiteKey: agentSuiteKey || '',
      searchQuery: '',
      page: 1,
      pageSize: 1,
    }),
    [agentSuiteKey]
  );

  const invitesCountFilters = membersCountFilters;

  const requestsCountFilters = useMemo(
    () => ({
      page: 1,
      pageSize: 1,
      status: 'PENDING' as const,
    }),
    []
  );

  const { data: membersData } = useSuiteMembers(
    membersCountFilters.agentSuiteKey,
    membersCountFilters.searchQuery,
    membersCountFilters.page,
    membersCountFilters.pageSize,
    !!agentSuiteKey
  );

  const { data: invitesData } = useSuiteInvites(
    invitesCountFilters.agentSuiteKey,
    invitesCountFilters.searchQuery,
    invitesCountFilters.page,
    invitesCountFilters.pageSize,
    !!agentSuiteKey
  );
  const { data: requestsData } = useSuiteJoinRequests(
    agentSuiteKey || '',
    requestsCountFilters,
    !!agentSuiteKey && canViewRequestsTab
  );

  // Initialize search queries from URL params
  useEffect(() => {
    const membersQuery = searchParams.get('msq') || '';
    const invitesQuery = searchParams.get('isq') || '';
    const requestsQuery = searchParams.get('rsq') || '';
    setMembersSearchQuery(membersQuery);
    setInvitesSearchQuery(invitesQuery);
    setRequestsSearchQuery(requestsQuery);
  }, [searchParams]);

  // Keep selected suite in sync with available options and navigation state
  useEffect(() => {
    if (suiteOptions.length === 0) {
      setCurrentSuite(undefined);
      return;
    }

    const suiteFromNavigation = navigationState?.agentSuiteKey
      ? suiteOptions.find(suite => suite.id === navigationState.agentSuiteKey)
      : null;

    const existingSuiteMatch = currentSuite
      ? suiteOptions.find(suite => suite.id === currentSuite.id)
      : null;

    const nextSuite =
      suiteFromNavigation || existingSuiteMatch || suiteOptions[0];

    if (
      !currentSuite ||
      currentSuite.id !== nextSuite.id ||
      currentSuite.name !== nextSuite.name ||
      currentSuite.icon !== nextSuite.icon
    ) {
      setCurrentSuite(nextSuite);
    }
  }, [suiteOptions, navigationState, currentSuite]);

  const showAlert = (
    message: string,
    type: 'error' | 'success' | 'warning'
  ) => {
    const toastType = type === 'warning' ? 'info' : type;
    notify(message, toastType);
  };

  const handleTabChange = (tabId: string) => {
    const newParams = new URLSearchParams(searchParams.toString());
    newParams.set('tab', tabId);
    setSearchParams(newParams);
  };

  const handleMembersSearchChange = (query: string) => {
    setMembersSearchQuery(query);
    const newParams = new URLSearchParams(searchParams.toString());
    if (query) {
      newParams.set('msq', query);
    } else {
      newParams.delete('msq');
    }
    setSearchParams(newParams);
  };

  const handleInvitesSearchChange = (query: string) => {
    setInvitesSearchQuery(query);
    const newParams = new URLSearchParams(searchParams.toString());
    if (query) {
      newParams.set('isq', query);
    } else {
      newParams.delete('isq');
    }
    setSearchParams(newParams);
  };

  const handleRequestsSearchChange = (query: string) => {
    setRequestsSearchQuery(query);
    const newParams = new URLSearchParams(searchParams.toString());
    if (query) {
      newParams.set('rsq', query);
    } else {
      newParams.delete('rsq');
    }
    setSearchParams(newParams);
  };

  const handleInviteMembers = () => {
    if (agentSuiteKey) {
      // Preserve current search params for return navigation
      const currentParams = searchParams.toString();

      const link = location.pathname.includes(ROUTES.DASHBOARD_SETTINGS)
        ? ROUTES.DASHBOARD_SETTINGS_MEMBERS_INVITE
        : ROUTES.DASHBOARD_MEMBERS_INVITE;
      navigate(link, {
        state: { agentSuiteKey, returnParams: currentParams },
      });
    }
  };

  // Get counts from API data - using the total from API response
  const membersCount = membersData?.total || 0;
  const invitesCount = invitesData?.total || 0;
  const requestsCount = canViewRequestsTab ? requestsData?.total || 0 : 0;

  // Tab configuration
  const tabs = [
    {
      id: 'members',
      label: (
        <div className="flex items-center gap-2">
          <span>Members</span>
          <span className="flex h-5 w-5 items-center justify-center rounded-full bg-primary text-xs font-medium text-white">
            {membersCount}
          </span>
        </div>
      ),
    },
    {
      id: 'invites',
      label: (
        <div className="flex items-center gap-2">
          <span>Invites</span>
          <span className="flex h-5 w-5 items-center justify-center rounded-full bg-primary text-xs font-medium text-white">
            {invitesCount}
          </span>
        </div>
      ),
    },
  ];

  if (canViewRequestsTab) {
    tabs.push({
      id: 'requests',
      label: (
        <div className="flex items-center gap-2">
          <span>Requests</span>
          <span className="flex h-5 w-5 items-center justify-center rounded-full bg-primary text-xs font-medium text-white">
            {requestsCount}
          </span>
        </div>
      ),
    });
  }

  return (
    <DashboardWithChatLayout reloadChatHistoryRef={reloadChatHistoryRef}>
      <div className="relative flex flex-col gap-4 p-4 sm:gap-6 sm:p-6">
        {/* Header */}
        <div className="flex items-center justify-between gap-4">
          <div className="flex items-center gap-3 sm:space-x-12">
            <div className="flex items-center gap-2">
              <Icons.Users className="text-primary" />{' '}
              <h2 className="font-bold text-blackOne md:text-lg">Members</h2>
            </div>
            <AgentsDropdown
              isOpen={isSuiteDropdownOpen}
              onToggle={() => setIsSuiteDropdownOpen(!isSuiteDropdownOpen)}
              currentItem={currentSuite}
              options={suiteOptions}
              onItemSelect={suite => {
                setCurrentSuite(suite);
                setIsSuiteDropdownOpen(false);
              }}
              placeholder="Select Suite"
              noOptionsMessage="No other suites available"
            />
          </div>

          {/* Desktop Add Members Button */}
          {canManageMembers && isUserMember && (
            <button
              onClick={handleInviteMembers}
              className="hidden h-[42px] items-center gap-2 rounded-md bg-primary px-4 text-sm font-normal text-white transition-colors hover:bg-primary/90 sm:flex"
            >
              <Plus className="h-4 w-4" strokeWidth={3} /> Add Members
            </button>
          )}
        </div>

        {/* Mobile Suite Dropdown and Plus Icon - Only on mobile */}
        <div className="flex items-center justify-between gap-3 sm:hidden">
          <AgentsDropdown
            isOpen={isSuiteMobileDropdownOpen}
            onToggle={() =>
              setIsSuiteMobileDropdownOpen(!isSuiteMobileDropdownOpen)
            }
            currentItem={currentSuite}
            options={suiteOptions}
            onItemSelect={suite => {
              setCurrentSuite(suite);
              setIsSuiteMobileDropdownOpen(false);
            }}
            placeholder="Select Suite"
            noOptionsMessage="No other suites available"
          />
          {canManageMembers && isUserMember && (
            <button
              onClick={handleInviteMembers}
              className="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-lg border border-grayThirteen text-grayTen"
            >
              <Plus className="h-5 w-5" />
            </button>
          )}
        </div>

        {/* Mobile Search Bar - Animated dropdown */}
        <div
          className={clsx(
            'overflow-hidden transition-all duration-300 ease-in-out sm:hidden',
            showMobileSearch
              ? 'max-h-20 opacity-100'
              : 'hidden max-h-0 opacity-0'
          )}
        >
          <Input
            type="text"
            placeholder="Search by name or email"
            className="h-10 w-full rounded-[10px] border border-grayThirteen py-2 pl-4 pr-10 text-sm placeholder:text-grayTen focus:outline-none focus:ring-0"
            endIcon={<Search className="mt-0.5 h-4 w-4" />}
            value={
              activeTab === 'members'
                ? membersSearchQuery
                : activeTab === 'invites'
                  ? invitesSearchQuery
                  : requestsSearchQuery
            }
            onChange={e => {
              if (activeTab === 'members') {
                handleMembersSearchChange(e.target.value);
              } else if (activeTab === 'invites') {
                handleInvitesSearchChange(e.target.value);
              } else {
                handleRequestsSearchChange(e.target.value);
              }
            }}
          />
        </div>

        {/* Tabs */}
        <div className="sm:mx-0">
          <AnimatedTabs
            tabs={tabs}
            activeTab={activeTab}
            onTabChange={handleTabChange}
            showContent={false}
          />
        </div>
        <div className="relative flex flex-col gap-4">
          <NotificationContainer
            notifications={notifications}
            onClose={dismiss}
            className="w-full"
            maxNotifications={3}
          />

          {/* Tab Content */}
          {activeTab === 'members' && agentSuiteKey ? (
            <MembersTab
              agentSuiteKey={agentSuiteKey}
              searchQuery={membersSearchQuery}
              onSearchChange={handleMembersSearchChange}
              onShowAlert={showAlert}
            />
          ) : activeTab === 'invites' && agentSuiteKey ? (
            <InvitesTab
              agentSuiteKey={agentSuiteKey}
              searchQuery={invitesSearchQuery}
              onSearchChange={handleInvitesSearchChange}
              onShowAlert={showAlert}
            />
          ) : activeTab === 'requests' &&
            canViewRequestsTab &&
            agentSuiteKey ? (
            <RequestsTab
              agentSuiteKey={agentSuiteKey}
              searchQuery={requestsSearchQuery}
              onSearchChange={handleRequestsSearchChange}
              onShowAlert={showAlert}
            />
          ) : null}
        </div>
      </div>
    </DashboardWithChatLayout>
  );
};

export default MembersSettings;
