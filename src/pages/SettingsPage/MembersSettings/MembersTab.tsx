import clsx from 'clsx';
import { Search } from 'lucide-react';
import React, { useState } from 'react';

import Pagination from '@/components/common/Pagination';
import { Input } from '@/components/ui';
import { useTimezone } from '@/context/TimezoneContext';
import { useDebounce } from '@/hooks/useDebounce';
import {
  useRemoveSuiteMemberMutation,
  useSuiteMembers,
  useUpdateSuiteMemberRoleMutation,
} from '@/hooks/useMembers';
import { useNotifications } from '@/hooks/useNotifications';
import { usePagination } from '@/hooks/usePagination';
import { useGetUserProfile } from '@/hooks/useUserProfile';

import ActionDropdown from '../../../components/common/ActionDropdown';
import DataTable, { Column } from '../../../components/ui/tables/DataTable';
import {
  mapApiRoleToUiRole,
  mapUiRoleToApiRole,
  Member,
  MemberRole,
} from '../../../types/members';
import {
  formatLastLogin,
  generateMemberInitials,
  getRoleColor,
  getRoleDisplayName,
} from '../../../utils/members';
import RemoveMemberModal from './RemoveMember';
import UpdateRoleModal from './UpdateRole';

interface MembersTabProps {
  agentSuiteKey: string;
  searchQuery: string;
  onSearchChange: (query: string) => void;
  onShowAlert: (message: string, type: 'error' | 'success' | 'warning') => void;
}

export const MembersTab: React.FC<MembersTabProps> = ({
  agentSuiteKey,
  searchQuery,
  onSearchChange,
}) => {
  const { page, setPage } = usePagination(1, 'membersPage');
  const [pageSize] = useState<number>(10);
  const debouncedSearchQuery = useDebounce(searchQuery, 500);

  const { notify } = useNotifications();

  const [isUpdateRoleModalOpen, setIsUpdateRoleModalOpen] = useState(false);
  const [isRemoveMemberModalOpen, setIsRemoveMemberModalOpen] = useState(false);
  const [selectedMember, setSelectedMember] = useState<{
    id: string;
    name: string;
    role: MemberRole;
  } | null>(null);

  const { data: userData } = useGetUserProfile();
  const { formatUserTimestamp } = useTimezone();

  const { data: membersData, isLoading: isLoadingMembers } = useSuiteMembers(
    agentSuiteKey,
    debouncedSearchQuery,
    page,
    pageSize,
    !!agentSuiteKey
  );

  const members = membersData?.members || [];

  const updateRoleMutation = useUpdateSuiteMemberRoleMutation();
  const removeMemberMutation = useRemoveSuiteMemberMutation();

  const handleUpdateRole = async (memberId: string, newRole: MemberRole) => {
    try {
      await updateRoleMutation.mutateAsync({
        memberId,
        payload: {
          agentSuiteKey: agentSuiteKey,
          memberRole: mapUiRoleToApiRole(newRole),
        },
      });

      notify('Role updated successfully', 'success');

      setIsUpdateRoleModalOpen(false);
      setSelectedMember(null);
    } catch (error) {
      const err = error as { response?: { data?: { message?: string } } };
      const errorMessage =
        err?.response?.data?.message || 'Failed to update role';

      notify(errorMessage);

      setIsUpdateRoleModalOpen(false);
      setSelectedMember(null);
    }
  };

  const handleRemoveMember = async (memberId: string) => {
    try {
      await removeMemberMutation.mutateAsync({
        memberId,
        agentSuiteKey: agentSuiteKey,
      });

      notify('Member removed successfully', 'success');
    } catch (error) {
      const err = error as { response?: { data?: { message?: string } } };
      const errorMessage =
        err?.response?.data?.message || 'Failed to remove member';

      notify(errorMessage);
    } finally {
      setIsRemoveMemberModalOpen(false);
      setSelectedMember(null);
    }
  };

  const memberColumns: Column<Member>[] = [
    {
      key: 'user',
      label: 'Name',
      render: (_, member) => (
        <div className="flex items-center">
          <div className="mr-3 flex h-10 w-10 items-center justify-center rounded-full bg-blackOne text-sm font-medium text-white">
            {generateMemberInitials(
              `${member?.user?.firstName} ${member?.user?.lastName}`
            )}
          </div>
          <div>
            <div className="font-medium text-blackOne">{`${member.user.firstName} ${member.user.lastName}`}</div>
            <div className="text-sm text-subText">{member?.user?.email}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'memberRoles',
      label: 'Role',
      render: (_, member) => {
        const role = mapApiRoleToUiRole(member.memberRoles[0]);
        return (
          <span
            className={clsx(
              'flex h-[34px] w-28 items-center justify-center rounded-lg px-4 text-xs font-medium',
              getRoleColor(role)
            )}
          >
            {getRoleDisplayName(role)}
          </span>
        );
      },
    },
    {
      key: 'claimedAgentSuite',
      label: 'Last Activity',
      render: (_, member) => (
        <span className="text-sm text-subText">
          {member?.user?.lastActivity
            ? (() => {
                const lastLoginText = formatLastLogin(member.user.lastActivity);
                return (
                  lastLoginText ||
                  formatUserTimestamp(member.user.lastActivity, 'date') ||
                  '--'
                );
              })()
            : '--'}
        </span>
      ),
    },
    {
      key: 'id',
      label: '',
      render: (_, member) => {
        const actions = [
          {
            label: 'Change Role',
            icon: null,
            onClick: () => {
              setSelectedMember({
                id: member.id,
                name: `${member?.user?.firstName} ${member?.user?.lastName}`,
                role: member.memberRoles[0] as MemberRole,
              });
              setIsUpdateRoleModalOpen(true);
            },
            variant: 'default' as const,
          },
          {
            label: 'Remove Member',
            icon: null,
            onClick: () => {
              setSelectedMember({
                id: member.id,
                name: `${member?.user?.firstName} ${member?.user?.lastName}`,
                role: member.memberRoles[0] as MemberRole,
              });
              setIsRemoveMemberModalOpen(true);
            },
            variant: 'danger' as const,
          },
        ];

        const currentUserRole =
          userData?.userInfo?.tenant?.claimedAgentSuites
            ?.find(suite => suite?.suite?.agentSuiteKey === agentSuiteKey)
            ?.members?.find(m => m?.user?.userId === userData?.userInfo?.userId)
            ?.memberRoles?.[0] || 'MEMBER';

        const canManageMembers =
          currentUserRole === 'MANAGER' || currentUserRole === 'LEAD';

        return canManageMembers ? <ActionDropdown actions={actions} /> : null;
      },
      className: 'text-right',
    },
  ];

  return (
    <>
      <div className="sm:space-y-6">
        {/* Notifications Container */}
        {/* Search */}
        <div className="flex items-center gap-3">
          <Input
            type="text"
            placeholder="Search by name or email"
            className="h-10 w-[300px] rounded-[10px] border border-grayThirteen py-2 pl-4 pr-10 text-sm placeholder:text-grayTen focus:outline-none focus:ring-0"
            endIcon={<Search className="mt-0.5 h-4 w-4" />}
            value={searchQuery}
            onChange={e => onSearchChange(e.target.value)}
          />
        </div>

        {/* Desktop Table - Hidden on mobile */}
        <div className="hidden flex-col gap-4 overflow-x-auto sm:flex">
          <DataTable<Member & Record<string, unknown>>
            data={members as unknown as (Member & Record<string, unknown>)[]}
            columns={
              memberColumns as unknown as Column<
                Member & Record<string, unknown>
              >[]
            }
            loading={isLoadingMembers}
            emptyMessage="No members found"
            rowColoring={true}
            rowColoringType="odd"
            getRowId={member => member.id}
          />
          {!isLoadingMembers && members && members?.length > 0 && (
            <Pagination
              currentPage={page}
              totalPages={
                membersData
                  ? Math.ceil(membersData.total / membersData.pageSize)
                  : 0
              }
              onPageChange={page => setPage(page)}
            />
          )}
        </div>

        {/* Mobile Card View - Hidden on desktop */}
        <div className="-mx-4 mt-4 flex flex-col sm:hidden">
          {isLoadingMembers ? (
            // Loading state
            Array.from({ length: 3 }).map((_, index) => (
              <div
                key={index}
                className={clsx('px-6 py-4', index % 2 === 0 && 'bg-[#FFF1EB]')}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    <div className="h-10 w-10 rounded-full bg-gray-200" />
                    <div className="space-y-2">
                      <div className="h-4 w-32 rounded bg-gray-200" />
                      <div className="h-3 w-40 rounded bg-gray-200" />
                    </div>
                  </div>
                  <div className="h-6 w-6 rounded bg-gray-200" />
                </div>
                <div className="mt-4 space-y-2">
                  <div className="h-3 w-20 rounded bg-gray-200" />
                  <div className="h-3 w-24 rounded bg-gray-200" />
                </div>
              </div>
            ))
          ) : members && members.length > 0 ? (
            <>
              {members.map((member, index) => {
                const role = mapApiRoleToUiRole(member.memberRoles[0]);
                const currentUserRole =
                  userData?.userInfo?.tenant?.claimedAgentSuites
                    ?.find(
                      suite => suite?.suite?.agentSuiteKey === agentSuiteKey
                    )
                    ?.members?.find(
                      m => m?.user?.userId === userData?.userInfo?.userId
                    )
                    ?.memberRoles?.[0]?.toLowerCase() || 'member';

                const canManageMembers =
                  currentUserRole === 'manager' || currentUserRole === 'lead';

                const actions = [
                  {
                    label: 'Change Role',
                    icon: null,
                    onClick: () => {
                      setSelectedMember({
                        id: member.id,
                        name: `${member?.user?.firstName} ${member?.user?.lastName}`,
                        role: member.memberRoles[0] as MemberRole,
                      });
                      setIsUpdateRoleModalOpen(true);
                    },
                    variant: 'default' as const,
                  },
                  {
                    label: 'Remove Member',
                    icon: null,
                    onClick: () => {
                      setSelectedMember({
                        id: member.id,
                        name: `${member?.user?.firstName} ${member?.user?.lastName}`,
                        role: member.memberRoles[0] as MemberRole,
                      });
                      setIsRemoveMemberModalOpen(true);
                    },
                    variant: 'danger' as const,
                  },
                ];

                return (
                  <div
                    key={member.id}
                    className={clsx('p-4', index % 2 === 0 && 'bg-[#FFF1EB]')}
                  >
                    {/* Header: Avatar, Name, Email, and Actions */}
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        <div className="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-blackOne text-sm font-medium text-white">
                          {generateMemberInitials(
                            `${member?.user?.firstName} ${member?.user?.lastName}`
                          )}
                        </div>
                        <div className="min-w-0 flex-1">
                          <div className="truncate font-medium text-blackOne">
                            {`${member.user.firstName} ${member.user.lastName}`}
                            {member?.user?.userId ===
                              userData?.userInfo?.userId && ' (You)'}
                          </div>
                          <div className="truncate text-sm text-subText">
                            {member?.user?.email}
                          </div>
                        </div>
                      </div>
                      {canManageMembers && <ActionDropdown actions={actions} />}
                    </div>

                    {/* Role and Last Activity */}
                    <div className="mt-4 flex flex-col items-center gap-2">
                      <div className="flex w-full items-center justify-between gap-2">
                        <span className="text-xs text-subText">Role</span>
                        <span
                          className={clsx(
                            'inline-flex items-center justify-center rounded-lg px-3 py-1.5 text-xs font-medium',
                            getRoleColor(role)
                          )}
                        >
                          {getRoleDisplayName(role)}
                        </span>
                      </div>
                      <div className="flex w-full items-center justify-between gap-2">
                        <span className="text-xs text-subText">
                          Last Activity
                        </span>
                        <span className="text-sm text-blackOne">
                          {(() => {
                            if (!member?.user?.lastActivity) return '--';
                            const lastLoginText = formatLastLogin(
                              member.user.lastActivity
                            );
                            return (
                              lastLoginText ||
                              formatUserTimestamp(
                                member.user.lastActivity,
                                'date'
                              ) ||
                              '--'
                            );
                          })()}
                        </span>
                      </div>
                    </div>
                  </div>
                );
              })}
              <div className="px-6 py-4">
                <Pagination
                  currentPage={page}
                  totalPages={
                    membersData
                      ? Math.ceil(membersData.total / membersData.pageSize)
                      : 0
                  }
                  onPageChange={page => setPage(page)}
                />
              </div>
            </>
          ) : (
            <div className="px-6 py-8 text-center text-sm text-subText">
              No members found
            </div>
          )}
        </div>
      </div>

      {/* Modals */}
      <UpdateRoleModal
        isOpen={isUpdateRoleModalOpen}
        onClose={() => {
          setIsUpdateRoleModalOpen(false);
          setSelectedMember(null);
        }}
        onUpdateRole={(newRole: MemberRole) => {
          handleUpdateRole(selectedMember?.id || '', newRole);
        }}
        userDetails={selectedMember}
        loading={updateRoleMutation.isPending}
      />

      <RemoveMemberModal
        isOpen={isRemoveMemberModalOpen}
        onClose={() => {
          setIsRemoveMemberModalOpen(false);
          setSelectedMember(null);
        }}
        onRemoveMember={memberId => handleRemoveMember(memberId)}
        member={selectedMember}
        loading={removeMemberMutation.isPending}
      />
    </>
  );
};
