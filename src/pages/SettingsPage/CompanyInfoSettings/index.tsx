import { AxiosError } from 'axios';
import CountryList from 'country-list-with-dial-code-and-flag';
import { ChevronLeft } from 'lucide-react';
import React, { useRef, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import Select, { StylesConfig } from 'react-select';

import EnhancedChatSidebar from '@/components/common/EnhancedChatSidebar';
import { Spinner } from '@/components/common/Loader';
import { Button, Input, NotificationContainer } from '@/components/ui';
import { ROUTES } from '@/constants/routes';
import { agentSuites as mockAgentsSuite } from '@/data/constants';
import { useGetAIAgentSuites } from '@/hooks/useAgents';
import { useNotifications } from '@/hooks/useNotifications';

interface CompanyInfoFormData {
  companyName: string;
  companyEmail: string;
  streetAddress1: string;
  streetAddress2: string;
  country: string;
  state: string;
  city: string;
  zipCode: string;
  countryCode: string;
  countryLabel: string;
  phoneNumber: string;
}

interface CountryOption {
  value: string;
  label: string;
  flag: string;
  code: string;
}

// Create country options from country-list-with-dial-code-and-flag data
const createCountryOptions = (): CountryOption[] => {
  return CountryList.getAll()
    .map(country => ({
      value: country.dial_code,
      label: `${country.name} ${country.dial_code}`,
      flag: country.flag,
      code: country.dial_code,
    }))
    .filter(country => country.value && country.value !== '+')
    .sort((a, b) => a.label.localeCompare(b.label));
};

const COUNTRY_OPTIONS = createCountryOptions();

const CompanyInfoSettings: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { notifyWithImage, notifications, dismiss } = useNotifications();

  // Get suite info from location state
  const agentSuiteKey = location.state?.agentSuiteKey;

  // Fetch suite data from API
  const { data: agentSuites = [], isLoading: isLoadingSuites } =
    useGetAIAgentSuites();
  const suite = agentSuites.find(s => s.agentSuiteKey === agentSuiteKey);

  // Get fallback image from mock data
  const suiteFallbackImage = mockAgentsSuite.find(
    mockAgent =>
      mockAgent.id.toLowerCase() === suite?.agentSuiteKey.toLowerCase()
  )?.image;

  // Use API data to get suite details
  const finalSuite = suite;

  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState<CompanyInfoFormData>({
    companyName: 'Alex ventures',
    companyEmail: '<EMAIL>',
    streetAddress1: '',
    streetAddress2: '',
    country: '',
    state: 'California',
    city: 'San Diego',
    zipCode: '22434',
    countryCode: '',
    countryLabel: '',
    phoneNumber: '',
  });

  // Ref for the country select component
  const countrySelectRef = useRef<any>(null);

  // Custom styles for react-select
  const selectStyles: StylesConfig<CountryOption> = {
    control: (provided: any, state: any) => ({
      ...provided,
      height: '40px',
      border: state.isFocused ? '2px solid #FF5C02' : '1px solid #d1d5db',
      borderRadius: '6px',
      boxShadow: 'none',
      '&:hover': {
        border: state.isFocused ? '2px solid #FF5C02' : '1px solid #d1d5db',
      },
    }),
    option: (provided, state) => ({
      ...provided,
      backgroundColor: state.isSelected
        ? '#FF5C02'
        : state.isFocused
          ? '#FFF5F0'
          : 'white',
      color: state.isSelected ? 'white' : '#374151',
      '&:hover': {
        backgroundColor: state.isSelected ? '#FF5C02' : '#FFF5F0',
      },
    }),
    menu: provided => ({
      ...provided,
      border: '1px solid #d1d5db',
      borderRadius: '6px',
      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
      maxHeight: '200px',
      zIndex: 9999,
    }),
    menuList: provided => ({
      ...provided,
      maxHeight: '200px',
      overflowY: 'auto' as const,
    }),
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // TODO: Implement API call to save company info
      console.log('Saving company info:', formData);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      notifyWithImage(
        'Company information updated successfully!',
        '',
        '',
        'success'
      );

      // Navigate back to the suite page
      navigate(ROUTES.DASHBOARD_AGENT_SUITE(agentSuiteKey || 'default'));
    } catch (error) {
      console.error('Error updating company info:', error);

      let errorMessage =
        'Failed to update company information. Please try again.';

      if (error instanceof AxiosError && error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      notifyWithImage(errorMessage, '', '');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    navigate(ROUTES.DASHBOARD_AGENT_SUITE(agentSuiteKey || 'default'));
  };

  // Show loading state while fetching suite data
  if (isLoadingSuites) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-center">
          <Spinner className="mx-auto mb-4 h-8 w-8" />
          <p className="text-lg text-gray-600">Loading suite information...</p>
        </div>
      </div>
    );
  }

  // Show error state if suite not found
  if (!finalSuite) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-center">
          <h1 className="mb-4 text-2xl font-bold text-black">
            Suite Not Found
          </h1>
          <p className="mb-4 text-gray-600">
            The agent suite could not be found.
          </p>
          <button
            onClick={() => navigate(ROUTES.DASHBOARD_AI_AGENTS)}
            className="rounded-lg bg-primary px-6 py-3 font-medium text-white transition-colors hover:bg-orange-15"
          >
            Back to AI Agents
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-full">
      {/* Chat Sidebar - LEFT SIDE */}
      <EnhancedChatSidebar />

      {/* Main Content - RIGHT SIDE */}
      <div className="flex-1 overflow-y-auto">
        <div className="flex w-full max-w-[850px] flex-col gap-4 p-8">
          {/* Header */}
          <div className="flex items-center gap-4">
            {/* Breadcrumb */}
            <button
              onClick={handleCancel}
              className="flex items-center gap-1 font-semibold text-blackTwo"
            >
              <ChevronLeft className="h-4 w-4 sm:h-6 sm:w-6" strokeWidth={2} />
              Agent Suite
            </button>
            <span className="text-blackTwo">›</span>
            <span className="text-gray-600">Company Information</span>
          </div>

          {/* Notifications Container */}
          <NotificationContainer
            notifications={notifications}
            onClose={dismiss}
            className="w-full"
            maxNotifications={3}
          />

          {/* Suite Header */}
          <div className="relative h-[198px] overflow-hidden rounded-xl bg-cover bg-center font-spartan">
            {/* Background Image */}
            <div
              className="absolute inset-0 bg-cover bg-center"
              style={{
                backgroundImage: finalSuite.avatar
                  ? `url(${finalSuite.avatar})`
                  : `url(${suiteFallbackImage})`,
              }}
            />
            {/* Dark Overlay */}
            <div className="absolute inset-0 bg-black/20" />
            {/* Content */}
            <div className="relative z-10 flex h-full flex-col justify-center p-6">
              <div className="flex w-fit items-center justify-center rounded-lg bg-white px-4 py-3">
                <h1 className="mt-1 text-[32px] font-bold leading-none">
                  {finalSuite.agentSuiteName}
                </h1>
              </div>
              <h2 className="mt-8 w-fit text-[20px] font-semibold text-white">
                {finalSuite.description}
              </h2>
              <div className="font-inter text-lg text-white">
                {finalSuite.roleDescription}
              </div>
            </div>
          </div>

          {/* Form */}
          <div className="w-full max-w-[528px]">
            <h2 className="mb-6 text-lg font-medium text-blackOne">
              Update Your Company Information
            </h2>

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Company Name & Email */}
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div>
                  <label
                    htmlFor="companyName"
                    className="mb-2 block text-xs font-normal text-grayTen"
                  >
                    Company Name
                  </label>
                  <Input
                    type="text"
                    id="companyName"
                    name="companyName"
                    value={formData.companyName}
                    onChange={handleInputChange}
                    required
                    className="w-full"
                  />
                </div>
                <div>
                  <label
                    htmlFor="companyEmail"
                    className="mb-2 block text-xs font-normal text-grayTen"
                  >
                    Company Email
                  </label>
                  <Input
                    type="email"
                    id="companyEmail"
                    name="companyEmail"
                    value={formData.companyEmail}
                    onChange={handleInputChange}
                    required
                    className="w-full"
                  />
                </div>
              </div>

              {/* Company Address */}
              <div>
                <h3 className="mb-4 text-base font-medium text-blackOne">
                  Company Address
                </h3>

                <div className="space-y-4">
                  <div>
                    <label
                      htmlFor="streetAddress1"
                      className="mb-2 block text-xs font-normal text-grayTen"
                    >
                      Street Address 1
                    </label>
                    <Input
                      type="text"
                      id="streetAddress1"
                      name="streetAddress1"
                      value={formData.streetAddress1}
                      onChange={handleInputChange}
                      placeholder="Enter street address 1"
                      className="w-full"
                    />
                  </div>

                  <div>
                    <label
                      htmlFor="streetAddress2"
                      className="mb-2 block text-xs font-normal text-grayTen"
                    >
                      Street Address 2
                    </label>
                    <Input
                      type="text"
                      id="streetAddress2"
                      name="streetAddress2"
                      value={formData.streetAddress2}
                      onChange={handleInputChange}
                      placeholder="Enter street address 2"
                      className="w-full"
                    />
                  </div>

                  <div>
                    <label
                      htmlFor="country"
                      className="mb-2 block text-xs font-normal text-grayTen"
                    >
                      Country
                    </label>
                    <select
                      id="country"
                      name="country"
                      value={formData.country}
                      onChange={handleInputChange}
                      className="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
                    >
                      <option value="">Select a country</option>
                      <option value="US">United States</option>
                      <option value="CA">Canada</option>
                      <option value="GB">United Kingdom</option>
                      <option value="AU">Australia</option>
                    </select>
                  </div>

                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <div>
                      <label
                        htmlFor="state"
                        className="mb-2 block text-xs font-normal text-grayTen"
                      >
                        State/Province
                      </label>
                      <select
                        id="state"
                        name="state"
                        value={formData.state}
                        onChange={handleInputChange}
                        className="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
                      >
                        <option value="">Select a state</option>
                        <option value="California">California</option>
                        <option value="New York">New York</option>
                        <option value="Texas">Texas</option>
                        <option value="Florida">Florida</option>
                      </select>
                    </div>
                    <div>
                      <label
                        htmlFor="city"
                        className="mb-2 block text-xs font-normal text-grayTen"
                      >
                        City
                      </label>
                      <input
                        type="text"
                        id="city"
                        name="city"
                        value={formData.city}
                        onChange={handleInputChange}
                        className="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <div>
                      <label
                        htmlFor="zipCode"
                        className="mb-2 block text-xs font-normal text-grayTen"
                      >
                        Zip/Postal Code
                      </label>
                      <Input
                        type="text"
                        id="zipCode"
                        name="zipCode"
                        value={formData.zipCode}
                        onChange={handleInputChange}
                        placeholder="Enter zip/postal code"
                        className="w-full"
                      />
                    </div>
                    <div>
                      <label
                        htmlFor="phone"
                        className="mb-2 block text-xs font-normal text-grayTen"
                      >
                        Phone Number
                      </label>
                      <div className="flex">
                        <div className="flex items-center rounded-l-md border border-r-0 border-gray-300 bg-gray-50 px-3">
                          <Select
                            ref={countrySelectRef}
                            className="w-12 text-sm"
                            options={COUNTRY_OPTIONS}
                            value={COUNTRY_OPTIONS.find(
                              option => option.label === formData.countryLabel
                            )}
                            onChange={selectedOption => {
                              const option = selectedOption as CountryOption;
                              setFormData(prev => ({
                                ...prev,
                                countryCode: option?.value || '',
                                countryLabel: option?.label || '',
                              }));
                            }}
                            styles={{
                              ...selectStyles,
                              control: (provided: any, state: any) => ({
                                ...provided,
                                height: 'auto',
                                minHeight: 'auto',
                                border: 'none',
                                boxShadow: 'none',
                                backgroundColor: 'transparent',
                                '&:hover': {
                                  border: 'none',
                                },
                              }),
                              valueContainer: provided => ({
                                ...provided,
                                padding: '0',
                                fontSize: '14px',
                              }),
                              input: provided => ({
                                ...provided,
                                margin: '0',
                                padding: '0',
                              }),
                              indicatorsContainer: provided => ({
                                ...provided,
                                padding: '0',
                              }),
                              dropdownIndicator: provided => ({
                                ...provided,
                                padding: '0 0 0 4px',
                              }),
                            }}
                            placeholder="🇺🇸"
                            isSearchable={true}
                            components={{
                              IndicatorSeparator: () => null,
                            }}
                          />
                        </div>
                        <input
                          type="tel"
                          name="phoneNumber"
                          value={formData.phoneNumber}
                          onChange={handleInputChange}
                          className="flex-1 rounded-r-md border border-gray-300 p-3 text-sm focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary"
                          placeholder="Enter phone number"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Form Actions */}
              <div className="flex w-full items-center justify-center gap-4">
                <Button
                  type="button"
                  className="w-full"
                  variant="outline"
                  onClick={handleCancel}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="w-full text-white"
                >
                  {isLoading && <Spinner className="h-4 w-4" />}
                  Save Changes
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CompanyInfoSettings;
