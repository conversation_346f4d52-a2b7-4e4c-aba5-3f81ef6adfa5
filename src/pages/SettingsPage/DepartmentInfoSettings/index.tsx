import CountryList from 'country-list-with-dial-code-and-flag';
import { ChevronLeft } from 'lucide-react';
import React, { useRef, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import Select, { StylesConfig } from 'react-select';

import EnhancedChatSidebar from '@/components/common/EnhancedChatSidebar';
import { Spinner } from '@/components/common/Loader';
import { Button, Input, NotificationContainer } from '@/components/ui';
import { ROUTES } from '@/constants/routes';
import { agentSuites as mockAgentsSuite } from '@/data/constants';
import { useGetAIAgentSuites } from '@/hooks/useAgents';
import { useNotifications } from '@/hooks/useNotifications';

interface DepartmentInfoFormData {
  departmentName: string;
  departmentContactName: string;
  departmentContactEmail: string;
  countryCode: string;
  countryLabel: string;
  departmentContactPhone: string;
}

interface CountryOption {
  value: string;
  label: string;
  flag: string;
  code: string;
}

// Create country options from country-list-with-dial-code-and-flag data
const createCountryOptions = (): CountryOption[] => {
  return CountryList.getAll()
    .map(country => ({
      value: country.dial_code,
      label: `${country.name} ${country.dial_code}`,
      flag: country.flag,
      code: country.dial_code,
    }))
    .filter(country => country.value && country.value !== '+')
    .sort((a, b) => a.label.localeCompare(b.label));
};

const COUNTRY_OPTIONS = createCountryOptions();

const DepartmentInfoSettings: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { notifyWithImage, notifications, dismiss } = useNotifications();

  // Get suite info from location state
  const agentSuiteKey = location.state?.agentSuiteKey;

  // Fetch suite data from API
  const { data: agentSuites = [], isLoading: isLoadingSuites } =
    useGetAIAgentSuites();
  const suite = agentSuites.find(s => s.agentSuiteKey === agentSuiteKey);

  // Get fallback image from mock data
  const suiteFallbackImage = mockAgentsSuite.find(
    mockAgent =>
      mockAgent.id.toLowerCase() === suite?.agentSuiteKey.toLowerCase()
  )?.image;

  // Use API data to get suite details
  const finalSuite = suite;

  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState<DepartmentInfoFormData>({
    departmentName: 'charlenereedco',
    departmentContactName: 'charlenereedco',
    departmentContactEmail: '<EMAIL>',
    countryCode: '',
    countryLabel: '',
    departmentContactPhone: '',
  });

  // Ref for the country select component
  const countrySelectRef = useRef<any>(null);

  // Custom styles for react-select
  const selectStyles: StylesConfig<CountryOption> = {
    control: (provided: any, state: any) => ({
      ...provided,
      height: '40px',
      border: state.isFocused ? '2px solid #FF5C02' : '1px solid #d1d5db',
      borderRadius: '6px',
      boxShadow: 'none',
      '&:hover': {
        border: state.isFocused ? '2px solid #FF5C02' : '1px solid #d1d5db',
      },
    }),
    option: (provided: any, state: any) => ({
      ...provided,
      backgroundColor: state.isSelected
        ? '#FF5C02'
        : state.isFocused
          ? '#FFF5F0'
          : 'white',
      color: state.isSelected ? 'white' : '#374151',
      '&:hover': {
        backgroundColor: state.isSelected ? '#FF5C02' : '#FFF5F0',
      },
    }),
    menu: provided => ({
      ...provided,
      border: '1px solid #d1d5db',
      borderRadius: '6px',
      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
      maxHeight: '200px',
      zIndex: 9999,
    }),
    menuList: provided => ({
      ...provided,
      maxHeight: '200px',
      overflowY: 'auto' as const,
    }),
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // TODO: Implement API call to save department info
      console.log('Saving department info:', formData);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      notifyWithImage(
        'Department information updated successfully!',
        '',
        '',
        'success'
      );

      // Navigate back to the suite page
      navigate(ROUTES.DASHBOARD_AGENT_SUITE(agentSuiteKey || 'default'));
    } catch (error) {
      console.error('Error updating department info:', error);
      notifyWithImage(
        'Failed to update department information. Please try again.',
        '',
        ''
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    navigate(ROUTES.DASHBOARD_AGENT_SUITE(agentSuiteKey || 'default'));
  };

  // Show loading state while fetching suite data
  if (isLoadingSuites) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-center">
          <Spinner className="mx-auto mb-4 h-8 w-8" />
          <p className="text-lg text-gray-600">Loading suite information...</p>
        </div>
      </div>
    );
  }

  // Show error state if suite not found
  if (!finalSuite) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-center">
          <h1 className="mb-4 text-2xl font-bold text-black">
            Suite Not Found
          </h1>
          <p className="mb-4 text-gray-600">
            The agent suite could not be found.
          </p>
          <button
            onClick={() => navigate(ROUTES.DASHBOARD_AI_AGENTS)}
            className="rounded-lg bg-primary px-6 py-3 font-medium text-white transition-colors hover:bg-orange-15"
          >
            Back to AI Agents
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-full">
      {/* Chat Sidebar - LEFT SIDE */}
      <EnhancedChatSidebar />

      {/* Main Content - RIGHT SIDE */}
      <div className="flex-1 overflow-y-auto">
        <div className="flex w-full max-w-[850px] flex-col gap-4 p-8">
          {/* Header */}
          <div className="flex items-center gap-4">
            {/* Breadcrumb */}
            <button
              onClick={handleCancel}
              className="flex items-center gap-1 font-semibold text-blackTwo"
            >
              <ChevronLeft className="h-4 w-4 sm:h-6 sm:w-6" strokeWidth={2} />
              Agent Suite
            </button>
            <span className="text-blackTwo">›</span>
            <span className="text-gray-600">Department Information</span>
          </div>

          {/* Notifications Container */}
          <NotificationContainer
            notifications={notifications}
            onClose={dismiss}
            className="w-full"
            maxNotifications={3}
          />

          {/* Suite Header */}
          <div className="relative h-[198px] overflow-hidden rounded-xl bg-cover bg-center font-spartan">
            {/* Background Image */}
            <div
              className="absolute inset-0 bg-cover bg-center"
              style={{
                backgroundImage: finalSuite.avatar
                  ? `url(${finalSuite.avatar})`
                  : `url(${suiteFallbackImage})`,
              }}
            />
            {/* Dark Overlay */}
            <div className="absolute inset-0 bg-black/20" />
            {/* Content */}
            <div className="relative z-10 flex h-full flex-col justify-center p-6">
              <div className="flex w-fit items-center justify-center rounded-lg bg-white px-4 py-3">
                <h1 className="mt-1 text-[32px] font-bold leading-none">
                  {finalSuite.agentSuiteName}
                </h1>
              </div>
              <h2 className="mt-8 w-fit text-[20px] font-semibold text-white">
                {finalSuite.description}
              </h2>
              <div className="font-inter text-lg text-white">
                {finalSuite.roleDescription}
              </div>
            </div>
          </div>

          {/* Form */}
          <div className="w-full max-w-[528px]">
            <h2 className="mb-6 text-lg font-medium text-blackOne">
              Update Your Department Information
            </h2>

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Department Name & Contact Name */}
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div>
                  <label
                    htmlFor="departmentName"
                    className="mb-2 block text-xs font-normal text-grayTen"
                  >
                    Department Name
                  </label>
                  <Input
                    type="text"
                    id="departmentName"
                    name="departmentName"
                    value={formData.departmentName}
                    onChange={handleInputChange}
                    className="w-full"
                    required
                  />
                </div>
                <div>
                  <label
                    htmlFor="departmentContactName"
                    className="mb-2 block text-xs font-normal text-grayTen"
                  >
                    Department Contact Name
                  </label>
                  <Input
                    type="text"
                    id="departmentContactName"
                    name="departmentContactName"
                    value={formData.departmentContactName}
                    onChange={handleInputChange}
                    className="w-full"
                    required
                  />
                </div>
              </div>

              {/* Contact Email & Phone */}
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div>
                  <label
                    htmlFor="departmentContactEmail"
                    className="mb-2 block text-xs font-normal text-grayTen"
                  >
                    Department Contact Email
                  </label>
                  <Input
                    type="email"
                    id="departmentContactEmail"
                    name="departmentContactEmail"
                    value={formData.departmentContactEmail}
                    onChange={handleInputChange}
                    className="w-full"
                    required
                  />
                </div>
                <div>
                  <label
                    htmlFor="departmentContactPhone"
                    className="mb-2 block text-xs font-normal text-grayTen"
                  >
                    Department Contact Phone Number
                  </label>
                  <div className="flex">
                    <div className="flex items-center rounded-l-md border border-r-0 border-gray-300 bg-gray-50 px-3">
                      <Select
                        ref={countrySelectRef}
                        className="w-12 text-sm"
                        options={COUNTRY_OPTIONS}
                        value={COUNTRY_OPTIONS.find(
                          option => option.label === formData.countryLabel
                        )}
                        onChange={selectedOption => {
                          const option = selectedOption as CountryOption;
                          setFormData(prev => ({
                            ...prev,
                            countryCode: option?.value || '',
                            countryLabel: option?.label || '',
                          }));
                        }}
                        styles={{
                          ...selectStyles,
                          control: (provided: any, state: any) => ({
                            ...provided,
                            height: 'auto',
                            minHeight: 'auto',
                            border: 'none',
                            boxShadow: 'none',
                            backgroundColor: 'transparent',
                            '&:hover': {
                              border: 'none',
                            },
                          }),
                          valueContainer: provided => ({
                            ...provided,
                            padding: '0',
                            fontSize: '14px',
                          }),
                          input: provided => ({
                            ...provided,
                            margin: '0',
                            padding: '0',
                          }),
                          indicatorsContainer: provided => ({
                            ...provided,
                            padding: '0',
                          }),
                          dropdownIndicator: provided => ({
                            ...provided,
                            padding: '0 0 0 4px',
                          }),
                        }}
                        placeholder="🇺🇸"
                        isSearchable={true}
                        components={{
                          IndicatorSeparator: () => null,
                        }}
                      />
                    </div>
                    <input
                      type="tel"
                      name="departmentContactPhone"
                      value={formData.departmentContactPhone}
                      onChange={handleInputChange}
                      className="flex-1 rounded-r-md border border-gray-300 p-3 text-sm focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary"
                      placeholder="Enter phone number"
                    />
                  </div>
                </div>
              </div>

              {/* Form Actions */}
              <div className="flex items-center justify-center gap-4">
                <Button
                  type="button"
                  onClick={handleCancel}
                  className="w-full"
                  variant="outline"
                  disabled={isLoading}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isLoading} className="w-full">
                  {isLoading ? (
                    <div className="flex items-center justify-center gap-2">
                      <Spinner className="h-4 w-4" />
                      <span className="text-sm">Saving...</span>
                    </div>
                  ) : (
                    'Save Changes'
                  )}
                  Save Changes
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DepartmentInfoSettings;
