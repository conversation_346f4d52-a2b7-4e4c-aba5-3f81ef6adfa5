import AutoPlayVideo from '@/components/ui/AutoPlayVideo';

import { starCircle } from '../../assets/images';
import { networkConnection } from '../../assets/videos';

const features = [
  'Let us enable your AI transformation',
  'We’ll integrate your existing business stack',
  'From manager to CEO, we surface the insights that matter',
  'We accelerate our roadmap to fill gaps in your operation',
];

export const EvolutionSection = () => {
  return (
    <section className="font-inter">
      <div className="container mx-auto mt-16 max-w-screen-3xl sm:px-6 md:mt-32 md:px-36">
        <div className="app-container flex flex-col items-center md:flex-row">
          {/* Left Column */}
          <div className="py-6 font-inter md:w-1/2 md:py-0">
            <div className="mb-6 text-left sm:text-center md:w-4/5 md:text-left">
              <h2 className="mb-2 text-xl font-semibold leading-[28px] text-gray-20 sm:text-3xl sm:font-bold md:mb-4 md:text-[40px]">
                Always Evolving.
              </h2>
              <h2 className="text-xl font-semibold leading-[28px] text-blue-midnight sm:text-3xl sm:font-bold md:text-[40px]">
                What's Coming Next.
              </h2>
              <p className="mt-4 text-left text-lg leading-[24px] text-subText">
                We're building the AI ecosystem to transform how work happens
                across the enterprise.
              </p>
            </div>

            <div className="mt-4 space-y-4 md:py-4">
              {features.map((feature, index) => (
                <div key={index} className="flex items-center gap-3">
                  <img
                    src={starCircle}
                    alt="✓"
                    className="h-4 w-4 flex-shrink-0"
                  />
                  <p className="text-sm">{feature}</p>
                </div>
              ))}
            </div>
          </div>

          {/* Right Column - Video */}
          <div className="z-20 md:w-1/2">
            <AutoPlayVideo videoSrc={networkConnection} />
          </div>
        </div>
      </div>
    </section>
  );
};
