import { motion } from 'framer-motion';
import { ArrowLeft, ArrowRight, ChevronLeft, ChevronRight } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';

import { Icons } from '@/assets/icons/DashboardIcons';
import { regis } from '@/assets/images';
import AppContainer from '@/components/common/AppContainer';
import EnhancedChatSidebar from '@/components/common/EnhancedChatSidebar';
import AgentSkeleton from '@/components/ui/AgentSkeleton';
import { ROUTES } from '@/constants/routes';
import { useTenant } from '@/context/TenantContext';
import {
  agentCategories,
  agentSuites as mockAgents,
  marketplaceAgents,
} from '@/data/constants';
import { useGetAIAgentsData } from '@/hooks/useAgents';
import { cn } from '@/lib/twMerge/cn';

const MarketplacePage = () => {
  const { activeAgent, setActiveAgent } = useTenant();
  const navigate = useNavigate();
  // Ensure the overall page is scrolled to top on initial navigation to this page.
  useEffect(() => {
    // Use a setTimeout to allow the route transition to complete before forcing scroll.
    const id = window.setTimeout(() => {
      try {
        window.scrollTo({ top: 0, left: 0, behavior: 'auto' });
      } catch (e) {
        window.scrollTo(0, 0);
      }
    }, 0);

    return () => window.clearTimeout(id);
  }, []);

  useEffect(() => {
    setActiveAgent('regis');
  }, [setActiveAgent, activeAgent]);

  const [selectedCategory, setSelectedCategory] = useState('all');
  const [mobileView, setMobileView] = useState<'chat' | 'marketplace'>(
    'marketplace'
  );
  const categoryScrollRef = useRef<HTMLDivElement>(null);
  const [currentCardIndex, setCurrentCardIndex] = useState(0);

  const handleCategoryFilter = (categoryId: string) => {
    if (categoryId === selectedCategory) {
      setSelectedCategory('all');
    } else {
      setSelectedCategory(categoryId);
    }
  };

  const scrollCategories = (direction: 'left' | 'right') => {
    if (categoryScrollRef.current) {
      const scrollAmount = 200;
      categoryScrollRef.current.scrollBy({
        left: direction === 'left' ? -scrollAmount : scrollAmount,
        behavior: 'smooth',
      });
    }
  };

  // Carousel navigation handlers
  const handlePrevCard = () => {
    setCurrentCardIndex(prev =>
      prev === 0 ? flattenedItems.length - 1 : prev - 1
    );
  };

  const handleNextCard = () => {
    setCurrentCardIndex(prev =>
      prev === flattenedItems.length - 1 ? 0 : prev + 1
    );
  };

  // Reset carousel when category changes
  useEffect(() => {
    setCurrentCardIndex(0);
  }, [selectedCategory]);

  const { agentSuites, agents, isLoadingSuites, isLoadingAgents } =
    useGetAIAgentsData();

  // Map categories to specific suite keys
  const categoryToSuitesMap: { [key: string]: string[] } = {
    COLLECTION_SERVICES: ['set-iq'], // Collections Services → SetIQ only
    UNDERWRITING_SERVICES: ['under-wing'], // Underwriting Services → UnderWing only
    ADMINISTRATIVE_SERVICES: ['admin-wing'], // Administrative Services → AdminWing only
    SALES_OPERATIONS: ['sales-wing'], // Sales Operations → SalesWing only
  };

  // Filter agent suites based on selected category
  const filteredSuites =
    selectedCategory === 'all'
      ? agentSuites
      : agentSuites.filter(suite => {
          const allowedSuiteKeys = categoryToSuitesMap[selectedCategory] || [];
          return allowedSuiteKeys.includes(suite.agentSuiteKey);
        });

  // Create a flattened list of suites and agents based on suite order
  // Each suite is followed by its agents
  const flattenedItems = filteredSuites.flatMap(suite => {
    // Get agents that belong to this suite
    const suiteAgents = agents.filter(
      agent => agent.agentSuiteKey === suite.agentSuiteKey
    );

    // Return suite followed by its agents
    return [
      { type: 'suite' as const, data: suite },
      ...suiteAgents.map(agent => ({ type: 'agent' as const, data: agent })),
    ];
  });

  const isLoading = isLoadingSuites || isLoadingAgents;

  return (
    <div className="min-h-full">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.3 }}
        className="mx-auto h-full max-w-screen-2xl"
      >
        <div className="max-h-screen font-inter">
          {/* Mobile Toggle Buttons */}
          <div className="mb-2 flex border-y p-2 md:hidden">
            <button
              onClick={() => setMobileView('marketplace')}
              className={`flex flex-1 items-center justify-center gap-2 px-4 py-3 text-sm font-medium transition-colors ${
                mobileView === 'marketplace'
                  ? 'border-b-2 border-primary bg-[#FFF1EB] text-blackOne'
                  : 'bg-white text-blackOne'
              }`}
            >
              <Icons.Agent className="h-4 w-4" />
              Agents Hub
            </button>
            <button
              onClick={() => setMobileView('chat')}
              className={`flex flex-1 items-center justify-center gap-2 px-4 py-3 text-sm font-medium transition-colors ${
                mobileView === 'chat'
                  ? 'border-b-2 border-primary bg-[#FFF1EB] text-blackOne'
                  : 'bg-white text-blackOne'
              }`}
            >
              <Icons.Chat2 />
              Chat With Regis
            </button>
          </div>

          <div className="flex h-full flex-1 gap-8 overflow-hidden md:h-[calc(100vh-100px)]">
            {/* Chat Sidebar */}
            <div
              className={`${mobileView === 'chat' ? 'block' : 'hidden'} md:block`}
            >
              <EnhancedChatSidebar />
            </div>

            {/* Agents Marketplace */}
            <div
              className={cn(
                'flex-1 overflow-y-auto',
                mobileView === 'marketplace' ? 'block' : 'hidden',
                'md:block'
              )}
            >
              <AppContainer
                className="space-y-4 px-4 sm:space-y-6 sm:px-6 lg:space-y-8"
                isPadding={false}
              >
                <div className="relative mb-6 bg-blue-midnight p-3 sm:p-4">
                  {/* Mobile Carousel with Navigation */}
                  <div className="relative sm:hidden">
                    <button
                      onClick={() => scrollCategories('left')}
                      className="absolute left-0 top-1/2 z-10 flex h-8 w-8 -translate-x-2 -translate-y-1/2 items-center justify-center rounded-full bg-white shadow-lg"
                      aria-label="Previous categories"
                    >
                      <ChevronLeft className="h-5 w-5 text-primary" />
                    </button>
                    <div
                      ref={categoryScrollRef}
                      className="overflow-x-auto [&::-webkit-scrollbar]:hidden"
                      style={{
                        scrollbarWidth: 'none',
                        msOverflowStyle: 'none',
                      }}
                    >
                      <div className="flex gap-2 px-8">
                        {agentCategories.map((category, index) => (
                          <button
                            key={index}
                            className={`flex-shrink-0 rounded-md ${
                              selectedCategory === category.id
                                ? 'bg-primary text-white hover:bg-darkOrangeTwo'
                                : 'bg-grayFifteen text-blackOne hover:bg-blue-50'
                            } touch-manipulation px-3 py-2 font-inter text-xs font-medium transition`}
                            onClick={() => handleCategoryFilter(category.id)}
                          >
                            {category.alias}
                          </button>
                        ))}
                      </div>
                    </div>
                    <button
                      onClick={() => scrollCategories('right')}
                      className="absolute right-0 top-1/2 z-10 flex h-8 w-8 -translate-y-1/2 translate-x-2 items-center justify-center rounded-full bg-white shadow-lg"
                      aria-label="Next categories"
                    >
                      <ChevronRight className="h-5 w-5 text-primary" />
                    </button>
                  </div>

                  {/* Desktop Wrapped View */}
                  <div className="hidden sm:block">
                    <div className="flex flex-wrap justify-center gap-2">
                      {agentCategories.map((category, index) => (
                        <button
                          key={index}
                          className={`rounded-md ${
                            selectedCategory === category.id
                              ? 'bg-primary text-white hover:bg-darkOrangeTwo'
                              : 'bg-grayFifteen text-blackOne hover:bg-blue-50'
                          } touch-manipulation px-4 py-2.5 font-inter text-sm font-medium transition`}
                          onClick={() => handleCategoryFilter(category.id)}
                        >
                          {category.alias}
                        </button>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="flex gap-4">
                  {isLoading ? (
                    Array.from({ length: 4 }).map((_, index) => (
                      <AgentSkeleton key={`skeleton-${index}`} />
                    ))
                  ) : flattenedItems.length === 0 ? (
                    <div className="col-span-2 flex h-[280px] w-full items-center justify-center sm:h-[338px]">
                      <div className="flex flex-col items-center justify-center text-center">
                        <div className="mb-4 flex h-[140px] w-full max-w-[280px] items-center justify-center rounded bg-gray-5 sm:h-[160px]">
                          <div className="space-y-2 sm:space-y-3">
                            <div className="mx-auto h-16 w-16 rounded-lg bg-grayNineTeen sm:h-20 sm:w-20" />
                            <div className="space-y-1.5 px-3 sm:space-y-2 sm:px-4">
                              <div className="h-2.5 w-28 rounded bg-grayNineTeen sm:h-3 sm:w-32" />
                              <div className="h-2.5 w-20 rounded bg-grayNineTeen sm:h-3 sm:w-24" />
                              <div className="h-2.5 w-24 rounded bg-grayNineTeen sm:h-3 sm:w-28" />
                            </div>
                          </div>
                        </div>
                        <p className="mt-3 text-xs text-darkGray sm:mt-4 sm:text-sm">
                          No agents or suites available yet for this category
                        </p>
                      </div>
                    </div>
                  ) : (
                    <div className="grid h-fit grid-cols-2 gap-3 sm:gap-4 lg:grid-cols-3">
                      {flattenedItems.map(item => {
                        if (item.type === 'suite') {
                          const suite = item.data;
                          return (
                            <div
                              key={`suite-${suite.agentSuiteKey}`}
                              className="flex h-[280px] w-full cursor-pointer flex-col items-center overflow-hidden rounded border transition-all hover:shadow-md sm:h-[338px] sm:max-w-[280px]"
                              onClick={() => {
                                setActiveAgent('');
                                navigate(
                                  ROUTES.AGENTS_DETAILS(suite.agentSuiteKey)
                                );
                              }}
                            >
                              <div>
                                <img
                                  src={suite.avatar}
                                  className="h-[140px] w-full bg-peachTwo object-cover sm:h-[160px]"
                                  alt={suite.agentSuiteName}
                                  onError={e => {
                                    const agentKey =
                                      suite.agentSuiteKey.toLowerCase();
                                    // Fallback to mock logo if agent avatar fails to load
                                    (e.target as HTMLImageElement).src =
                                      mockAgents.filter(
                                        agent =>
                                          agent.id.toLowerCase() === agentKey
                                      )[0].image;
                                  }}
                                />
                                <div className="flex flex-col gap-1.5 px-2 py-2 text-blackOne md:gap-2 md:px-2.5 md:py-3">
                                  <div className="w-fit rounded border border-grayNine bg-grayNineTeen px-1.5 py-[2px] text-xs font-bold md:px-2 md:text-sm">
                                    {suite.agentSuiteName}
                                  </div>
                                  <p className="line-clamp-2 whitespace-pre-line text-xs font-bold leading-tight md:text-sm md:leading-5">
                                    {suite.description}
                                  </p>
                                  <p className="line-clamp-2 text-xs text-darkGray md:text-sm">
                                    {suite.roleDescription}
                                  </p>
                                </div>
                              </div>
                            </div>
                          );
                        } else {
                          const agent = item.data;
                          return (
                            <div
                              key={`agent-${agent.agentKey}`}
                              className="flex h-[280px] w-full cursor-pointer flex-col overflow-hidden rounded border bg-white transition-all hover:shadow-md sm:h-[338px] sm:max-w-[280px]"
                              onClick={() => {
                                setActiveAgent(agent.agentKey);
                                navigate(
                                  ROUTES.AGENTS_DETAILS(agent.agentSuiteKey!)
                                );
                              }}
                            >
                              <div className="bg-peachTwo">
                                <img
                                  src={agent.avatar || '/placeholder-agent.png'}
                                  className="h-[140px] w-full object-contain sm:h-[160px]"
                                  alt={agent.agentName}
                                  onError={e => {
                                    // Fallback to mock logo if agent avatar fails to load
                                    const mockAgent = marketplaceAgents.find(
                                      mockAgent =>
                                        mockAgent.name.toLowerCase() ===
                                        agent.agentName.toLowerCase()
                                    );
                                    (e.target as HTMLImageElement).src =
                                      mockAgent?.image ||
                                      '/placeholder-agent.png';
                                  }}
                                />
                              </div>
                              <div className="flex flex-col gap-1.5 px-2 py-2 text-blackOne md:gap-2 md:px-2.5 md:py-3">
                                <div className="w-fit rounded border border-grayNine bg-grayNineTeen px-1.5 py-[2px] text-xs font-bold md:px-2 md:text-sm">
                                  {agent.agentName}
                                </div>
                                <p className="line-clamp-2 whitespace-pre-line text-xs font-semibold leading-tight md:text-sm md:leading-5">
                                  {agent.description}
                                </p>
                                <p className="line-clamp-2 text-xs text-darkGray md:text-sm">
                                  {agent.roleDescription}
                                </p>
                              </div>
                            </div>
                          );
                        }
                      })}

                      {/* Chat with Regis promotional card - always at the bottom */}
                      <div
                        className="flex h-[280px] w-full cursor-pointer flex-col items-center overflow-hidden rounded border transition-all hover:shadow-md sm:h-[338px] sm:max-w-[280px]"
                        onClick={() => {
                          setActiveAgent('regis');
                          setMobileView('chat');
                          // Focus on chat input after view switches
                          setTimeout(() => {
                            const chatInput = document.querySelector(
                              'textarea[placeholder*="ready"]'
                            ) as HTMLTextAreaElement;
                            if (chatInput) {
                              chatInput.focus();
                            }
                          }, 100);
                        }}
                      >
                        <img
                          src={regis}
                          className="h-[140px] w-full bg-[#587C72] object-contain sm:h-[160px]"
                          alt="Regis"
                        />
                        <div className="flex flex-col items-center px-4 py-3 text-blackOne md:px-8 md:py-4">
                          <div className="w-fit rounded-lg border border-blackOne px-1.5 py-1 font-bold">
                            <div className="hidden md:block">
                              <ArrowLeft />
                            </div>
                            <div className="block md:hidden">
                              <ArrowRight />
                            </div>
                          </div>
                          <p className="mt-2 text-sm md:mt-3 md:text-base">
                            Chat with Regis
                          </p>
                          <p className="text-center text-base font-semibold text-darkGray md:text-lg">
                            See what's next?
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </AppContainer>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default MarketplacePage;
