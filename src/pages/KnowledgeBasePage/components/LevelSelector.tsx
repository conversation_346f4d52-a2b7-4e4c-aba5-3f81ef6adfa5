import { ChevronRight } from 'lucide-react';
import React from 'react';

import { agentSelector, setIq } from '../../../assets/images';
import { agentSuites as mockAgentsSuite } from '../../../data/constants';
import { AgentSuite } from '../../../types/user';

export type UploadLevel = 'suite' | 'agent';

interface LevelOption {
  id: UploadLevel;
  title: string;
  description: string;
  image: string;
  showImage: boolean;
}

interface LevelSelectorProps {
  currentSuite?: AgentSuite;
  selectedLevel?: UploadLevel;
  onLevelSelect: (level: UploadLevel) => void;
  onNext: () => void;
}

const LevelSelector: React.FC<LevelSelectorProps> = ({
  currentSuite,
  onLevelSelect,
  onNext,
}) => {
  const levelOptions: LevelOption[] = [
    {
      id: 'suite' as const,
      title: 'Suite Level',
      description: 'Shared by all agents in this suite',
      image: currentSuite?.avatar || setIq,
      showImage: true,
    },
    {
      id: 'agent' as const,
      title: 'Agent Level',
      description: 'Only used by the selected agent',
      image: agentSelector,
      showImage: true,
    },
  ];

  const handleCardClick = (level: UploadLevel) => {
    onLevelSelect(level);
    onNext();
  };

  return (
    <div className="flex flex-col items-start p-4 sm:p-0">
      {/* Header */}
      <h2 className="mb-4 flex items-center gap-2 text-base font-medium capitalize text-subText sm:mb-6 sm:text-xl">
        Select Level <ChevronRight /> Upload Knowledge Base
      </h2>

      {/* Selection Cards */}
      <div className="grid grid-cols-2 gap-4 sm:gap-6">
        {levelOptions.map(option => (
          <div
            key={option.id}
            className="max-w-[290px] cursor-pointer overflow-hidden rounded-xl border border-gray-200 bg-white transition-shadow hover:border-primary sm:rounded-2xl"
            onClick={() => handleCardClick(option.id)}
          >
            {/* Card Image */}
            <img
              src={option.image}
              className="object- h-[120px] w-full object-cover object-top sm:h-56"
              alt={option.title}
              onError={e => {
                // Fallback to mock logo if agent avatar fails to load
                (e.target as HTMLImageElement).src = mockAgentsSuite.filter(
                  agent => agent.id.toLowerCase() === option.id.toLowerCase()
                )[0].image;
              }}
            />

            {/* Card Content */}
            <div className="flex flex-col gap-2 p-4 text-blackOne sm:p-6">
              <div className="text-base font-semibold sm:text-lg">
                {option.title}
              </div>
              <p className="text-sm">{option.description}</p>
              <button
                className="mt-4 rounded-lg bg-primary px-4 py-2 text-white transition-colors hover:bg-orange-15"
                onClick={() => handleCardClick(option.id)}
              >
                Select Level
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default LevelSelector;
