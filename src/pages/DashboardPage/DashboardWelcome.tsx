import React from 'react';
import { useNavigate } from 'react-router-dom';

import AgentSelectionLayout from '@/components/layout/AgentSelectionLayout';
import { useMediaQuery } from '@/hooks/useMediaQuery';

import { dashboardIcons, dashboardIconsMobile } from '../../assets/images';
import { ROUTES } from '../../constants/routes';
import { useGetUserProfile } from '../../hooks/useUserProfile';
import { UserBasicInfoPayload } from '../../types/user';

const DashboardWelcome: React.FC = () => {
  const navigate = useNavigate();
  const { data: userData } = useGetUserProfile<UserBasicInfoPayload>();
  const isMobile = useMediaQuery('(max-width: 768px)');

  // Check if user has claimed the specific agents suite
  const isAgentSuiteClaimed = (suiteKey: string) => {
    return userData?.userInfo?.tenant?.claimedAgentSuites?.some(
      claimedSuite => claimedSuite.suite.agentSuiteKey === suiteKey
    );
  };

  return (
    <>
      <div className="hidden h-full flex-col gap-8 p-4 sm:p-8">
        {/* Header */}
        <div className="text-start">
          <h1 className="mb-2 font-semibold text-blackOne sm:text-2xl">
            Dashboard
          </h1>

          {/* Hero Section */}
          <div className="flex h-[140px] max-w-4xl items-center overflow-hidden rounded-2xl bg-[#040721] text-white">
            <div className="flex w-full items-center justify-between">
              <div className="p-6 text-left">
                <h2 className="mb-2 text-lg font-bold">
                  Actionable Intelligence across all Agentic AI agents.
                </h2>
                <p className="text-gray-300">
                  Compare performance, monitor activity, and act on daily
                  insights.
                </p>
              </div>
              <div className="relative mr-8 h-full w-[158px]">
                <img src={dashboardIcons} alt="bg" className="h-full w-full" />
              </div>
            </div>
          </div>
        </div>
      </div>
      <AgentSelectionLayout
        title="Actionable Intelligence across all Agentic AI agents."
        description="Compare performance, monitor activity, and act on daily insights."
        className=" w-full bg-[#040721] text-white"
        bgImage={isMobile ? dashboardIconsMobile : dashboardIcons}
        pageType="dashboard"
        onAgentSuiteClick={suite => {
          if (!isAgentSuiteClaimed(suite.agentSuiteKey)) {
            navigate(
              ROUTES.DASHBOARD_ANALYTICS_ACTIVATE_SUITE(suite.agentSuiteKey)
            );
          } else {
            navigate(
              ROUTES.DASHBOARD_ANALYTICS_SUITE_DETAILS(suite.agentSuiteKey)
            );
          }
        }}
      />
    </>
  );
};

export default DashboardWelcome;
