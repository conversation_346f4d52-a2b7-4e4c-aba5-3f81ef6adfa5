import clsx from 'clsx';
import React from 'react';

import { useTimezone } from '@/context/TimezoneContext';
import { AssignmentLogDetailsFormData } from '@/types/assignmentLog';

interface LogBasicInfoProps {
  log: AssignmentLogDetailsFormData;
  editForm: Partial<AssignmentLogDetailsFormData>;
  isEditing: boolean;
  onInputChange: (
    field: keyof AssignmentLogDetailsFormData,
    value: string
  ) => void;
}

const AssignmentLogBasicInfo: React.FC<LogBasicInfoProps> = ({
  log,
  editForm,
  isEditing,
  onInputChange,
}) => {
  const { formatUserTimestamp } = useTimezone();

  return (
    <div className="space-y-8">
      {/* Top Section - Time, Date Created, Due Date */}
      <div className="mb-8 grid grid-cols-3 gap-6">
        {/* <div className="rounded-lg border border-[#DFEAF2] bg-[#FFFAF7] p-4">
          <h3 className="mb-2 text-sm font-medium text-grayTen sm:text-base">
            Time (EST)
          </h3>
          <p className="text-base text-subText">{log.time}</p>
        </div> */}

        <div className="rounded-lg border border-[#DFEAF2] bg-[#FFFAF7] p-4">
          <h3 className="mb-2 text-sm font-medium text-grayTen sm:text-base">
            Date Created
          </h3>
          <p className="text-base text-subText">
            {formatUserTimestamp(log.dateCreated, 'date')}
          </p>
        </div>

        <div
          className={clsx(
            'rounded-lg p-4',
            'border border-[#DFEAF2] bg-[#FFFAF7]',
            isEditing && 'border-primary bg-[#FFFAF7]'
          )}
        >
          <h3 className="mb-2 text-sm font-medium text-grayTen sm:text-base">
            Due Date
          </h3>
          {isEditing ? (
            <input
              type="date"
              value={editForm.dueDate || ''}
              onChange={e => onInputChange('dueDate', e.target.value)}
              className="w-full rounded-none border border-primary bg-white p-3 text-base text-subText outline-none focus:border-primary focus:outline-none"
              placeholder="Enter due date"
            />
          ) : (
            <p className="text-base text-subText">
              {formatUserTimestamp(log.dueDate, 'date')}
            </p>
          )}
        </div>
      </div>

      {/* Task Title */}
      <div className="hidden rounded-lg border border-[#DFEAF2] p-4">
        <h3 className="mb-3 text-sm font-medium text-grayTen">Task Title</h3>
        {isEditing ? (
          <input
            type="text"
            value={editForm.assignmentTitle || ''}
            onChange={e => onInputChange('assignmentTitle', e.target.value)}
            className="w-full rounded-none border border-primary bg-white p-3 text-base text-subText outline-none focus:border-primary focus:outline-none"
            placeholder="Enter task title"
          />
        ) : (
          <p className="break-all text-base text-subText">
            {log.assignmentTitle || '--'}
          </p>
        )}
      </div>

      {/* Description */}
      <div className="rounded-lg border border-[#DFEAF2] p-4">
        <h3 className="mb-3 text-sm font-medium text-grayTen">Description</h3>
        {isEditing ? (
          <textarea
            value={editForm.description || ''}
            onChange={e => onInputChange('description', e.target.value)}
            rows={8}
            className="w-full resize-none rounded-none border border-primary bg-white p-3 text-subText outline-none focus:border-primary focus:outline-none focus:ring-0"
            placeholder="Enter task description"
          />
        ) : (
          <p className="break-all leading-relaxed text-subText">
            {log.description}
          </p>
        )}
      </div>
    </div>
  );
};

export default AssignmentLogBasicInfo;
