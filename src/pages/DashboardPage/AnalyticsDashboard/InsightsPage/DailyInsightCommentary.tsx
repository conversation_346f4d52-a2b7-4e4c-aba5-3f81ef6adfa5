import React from 'react';

import { Icons } from '@/assets/icons/DashboardIcons';
import { insightsEmptyStateBg } from '@/assets/images';
import { useTimezone } from '@/context/TimezoneContext';
import { DailyInsight } from '@/types/dashboard';

interface InsightData {
  id: string;
  insight: string;
  createdAt: string;
  type: 'warning' | 'info' | 'suggestion';
  isLast?: boolean;
}

const InsightItem: React.FC<InsightData> = ({
  insight,
  createdAt,
  isLast = false,
}) => {
  const { formatUserTimestamp } = useTimezone();
  return (
    <div
      className={`animate-fadeIn flex flex-row items-start gap-3 rounded-xl border border-gray-100 bg-white px-4 py-3 transition-all duration-150 sm:rounded-none sm:border-x-0 sm:border-b sm:border-t-0 sm:px-0 ${
        isLast ? 'sm:border-b-0' : ''
      }`}
    >
      <div className="flex h-9 w-9 shrink-0 items-center justify-center rounded-2xl bg-[#F3F6FF]">
        <Icons.ChipExtractionRound className="h-[18px] w-[18px]" />
      </div>
      <div className="flex flex-col items-start gap-2">
        <span className="text-xs font-medium text-[#44475B] sm:text-sm">
          {formatUserTimestamp(createdAt, 'full')}
        </span>{' '}
        <p className="text-xs font-normal leading-[18px] text-[#4A4E69] sm:text-sm sm:leading-[22px]">
          {insight}
        </p>
      </div>
    </div>
  );
};

interface DailyInsightCommentaryProps {
  insights?: DailyInsight[];
  isLoading?: boolean;
  isError?: boolean;
}

const DailyInsightCommentary: React.FC<DailyInsightCommentaryProps> = ({
  insights,
  isLoading,
  isError,
}) => {
  if (isLoading) {
    return (
      <div className="space-y-4 p-4">
        {[...Array(3)].map((_, index) => (
          <div
            key={index}
            className="flex animate-pulse items-center space-x-4"
          >
            <div className="h-12 w-12 rounded-2xl bg-gray-200"></div>
            <div className="h-5 w-3/4 rounded-full bg-gray-200"></div>
          </div>
        ))}
      </div>
    );
  }

  if (isError) {
    return (
      <div className="py-8 text-center">
        <p className="text-red-500">
          Failed to load insights. Please try again.
        </p>
      </div>
    );
  }

  if (!insights || insights?.length === 0) {
    return (
      <div
        className="relative flex h-[171px] w-full items-start justify-start overflow-hidden rounded-lg p-5 sm:h-[295px] sm:items-center sm:justify-center sm:p-16"
        style={{
          backgroundImage: `url(${insightsEmptyStateBg})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
        }}
      >
        {/* Dark overlay */}
        <div
          className="absolute inset-0"
          style={{
            backgroundColor: '#000000B2',
          }}
        />

        {/* Content */}
        <div className="relative z-10 flex flex-col items-start justify-center gap-6 sm:items-center">
          {/* Icon with warning background */}
          <div className="flex h-9 w-9 items-center justify-center rounded-lg bg-warning sm:h-16 sm:w-16 sm:rounded-2xl">
            <Icons.ChipExtractionRound className="h-6 w-6 text-white sm:h-8 sm:w-8" />
          </div>

          {/* Text content */}
          <div className="flex flex-col items-start gap-3 sm:items-center">
            <h3 className="text-center text-base font-semibold leading-[100%] text-white">
              No insights available today.
            </h3>
            <p className="text-left text-sm font-normal leading-[24px] text-white sm:text-center sm:text-base">
              As you activate your first agent tasks, this
              <br />
              space will come alive with real-time insights.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-3">
      {insights?.map((insight, index) => (
        <InsightItem
          key={insight.id}
          id={insight.id}
          insight={insight.observation}
          createdAt={insight.createdAt}
          isLast={index === insights.length - 1}
          type={'info'}
        />
      ))}
    </div>
  );
};

export default DailyInsightCommentary;
