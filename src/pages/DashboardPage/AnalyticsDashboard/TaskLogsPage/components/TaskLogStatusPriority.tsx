import clsx from 'clsx';
import React from 'react';

import { Icons } from '@/assets/icons/DashboardIcons';
import {
  TaskLogDetailsFormData,
  TaskLogPriorityDisplay,
  TaskLogStatusDisplay,
} from '@/types/taskLog';

import CustomDropdown, { DropdownOption } from './CustomDropdown';

interface TaskLogStatusPriorityProps {
  taskLog: TaskLogDetailsFormData;
  editForm: Partial<TaskLogDetailsFormData>;
  isEditing: boolean;
  onSelectChange: (field: keyof TaskLogDetailsFormData, value: string) => void;
}

const TaskLogStatusPriority: React.FC<TaskLogStatusPriorityProps> = ({
  taskLog,
  editForm,
  isEditing,
  onSelectChange,
}) => {
  const statusOptionStrings: TaskLogStatusDisplay[] = [
    'Queued',
    'Not Started',
    'In Progress',
    'Completed',
    'Failed',
    'Escalated',
  ];
  const priorityOptionStrings: TaskLogPriorityDisplay[] = [
    'Low',
    'Medium',
    'High',
  ];

  const getPriorityColor = (priority: string) => {
    const colorMap = {
      Low: 'bg-[#23BD33] text-white',
      Medium: 'bg-[#FBA320] text-white',
      High: 'bg-[#CE1111] text-white',
    };
    return (
      colorMap[priority as keyof typeof colorMap] || 'bg-gray-500 text-white'
    );
  };

  const getStatusColor = (status: string) => {
    const colorMap = {
      Completed: 'bg-blackOne text-white',
      'In Progress': 'bg-purpleOne text-white',
      'Not Started': 'bg-grayTen text-white',
      Queued: 'bg-blue-500 text-white',
      Failed: 'bg-red-500 text-white',
      Escalated: 'bg-orange-600 text-white',
    };
    return (
      colorMap[status as keyof typeof colorMap] || 'bg-gray-500 text-white'
    );
  };

  const getPriorityIcon = (priority: string) => {
    //use switch case to return the correct icon
    switch (priority) {
      case 'Low':
        return <Icons.PriorityLow className="h-3.5 w-4" />;
      case 'Medium':
        return <Icons.PriorityMedium className="h-3.5 w-4" />;
      case 'High':
        return <Icons.PriorityHigh className="h-3.5 w-4" />;
      default:
        return null;
    }
  };

  const statusOptions: DropdownOption[] = statusOptionStrings.map(s => ({
    value: s,
    label: s,
  }));

  const priorityOptions: DropdownOption[] = priorityOptionStrings.map(p => ({
    value: p,
    label: p,
    icon: getPriorityIcon(p),
  }));

  return (
    <div className="grid grid-cols-2 gap-4 sm:gap-6">
      <div className="rounded-none border border-[#DFEAF2] p-4">
        <h3 className="mb-3 text-sm font-medium text-grayTen">Status</h3>
        {isEditing ? (
          <CustomDropdown
            options={statusOptions}
            value={editForm.status || taskLog.status}
            onChange={value => onSelectChange('status', value)}
            buttonClassName={clsx(
              'w-fit min-w-[136px] h-8 sm:h-[44px]',
              getStatusColor(editForm.status || taskLog.status)
            )}
            renderButtonContent={selectedOption => (
              <span className="flex-grow text-left">
                {selectedOption?.label}
              </span>
            )}
            dropdownClassName="w-[150px]!"
          />
        ) : (
          <span
            className={clsx(
              'inline-flex h-8 items-center gap-2 rounded-full px-6 text-xs font-medium sm:h-[44px] sm:text-sm',
              getStatusColor(taskLog.status)
            )}
          >
            {taskLog.status}
          </span>
        )}
      </div>

      <div className="rounded-none border border-[#DFEAF2] p-4">
        <h3 className="mb-3 text-sm font-medium text-grayTen">Priority</h3>
        {isEditing ? (
          <CustomDropdown
            options={priorityOptions}
            value={editForm.priority || taskLog.priority}
            onChange={value => onSelectChange('priority', value)}
            buttonClassName={clsx(
              'w-fit min-w-[136px] h-8 sm:h-[44px]',
              getPriorityColor(editForm.priority || taskLog.priority)
            )}
            renderButtonContent={selectedOption => (
              <span className="flex items-center gap-2">
                {selectedOption?.icon}
                {selectedOption?.label}
              </span>
            )}
            dropdownClassName="w-[150px]!"
          />
        ) : (
          <span
            className={clsx(
              'inline-flex h-8 items-center gap-2 rounded-full px-6 text-xs font-medium sm:h-[44px] sm:text-sm',
              getPriorityColor(taskLog.priority)
            )}
          >
            {getPriorityIcon(taskLog.priority)}
            {taskLog.priority}
          </span>
        )}
      </div>
    </div>
  );
};

export default TaskLogStatusPriority;
