import clsx from 'clsx';
import React from 'react';

import { useTimezone } from '@/context/TimezoneContext';
import { TaskLogDetailsFormData } from '@/types/taskLog';

interface LogBasicInfoProps {
  taskLog: TaskLogDetailsFormData;
  editForm: Partial<TaskLogDetailsFormData>;
  isEditing: boolean;
  onInputChange: (field: keyof TaskLogDetailsFormData, value: string) => void;
}

const LogBasicInfo: React.FC<LogBasicInfoProps> = ({
  taskLog,
  editForm,
  isEditing,
  onInputChange,
}) => {
  const { formatUserTimestamp } = useTimezone();

  return (
    <div className="space-y-8">
      {/* Top Section - Time, Date Created, Due Date */}
      <div className="mb-8 grid grid-cols-2 gap-4 sm:grid-cols-3 sm:gap-6">
        <div className="col-span-2 rounded-lg border border-[#DFEAF2] bg-[#FFF1EB] p-4 sm:col-span-1">
          <h3 className="mb-2 text-sm font-medium text-grayTen sm:text-base">
            Time
          </h3>
          <p className="text-base text-subText">{taskLog.time}</p>
        </div>

        <div className="rounded-lg border border-[#DFEAF2] bg-[#FFF1EB] p-4">
          <h3 className="mb-2 text-sm font-medium text-grayTen sm:text-base">
            Date Created
          </h3>
          <p className="text-base text-subText">
            {formatUserTimestamp(taskLog.dateCreated, 'date')}
          </p>
        </div>

        <div
          className={clsx(
            'rounded-lg p-4',
            'border border-[#DFEAF2] bg-[#FFF1EB]',
            isEditing && 'border-primary bg-[#FFFAF7]'
          )}
        >
          <h3 className="mb-2 text-sm font-medium text-grayTen sm:text-base">
            Due Date
          </h3>
          {isEditing ? (
            <input
              type="date"
              value={editForm.dueDate || ''}
              onChange={e => onInputChange('dueDate', e.target.value)}
              className="w-full rounded-none border border-primary bg-white p-3 text-base text-subText outline-none focus:border-primary focus:outline-none"
              placeholder="Enter due date"
            />
          ) : (
            <p className="text-base text-subText">
              {formatUserTimestamp(taskLog.dueDate, 'date')}
            </p>
          )}
        </div>
      </div>

      {/* Task Title */}
      <div className="rounded-lg border border-[#DFEAF2] p-4">
        <h3 className="mb-3 text-sm font-medium text-grayTen">Task Title</h3>
        {isEditing ? (
          <input
            type="text"
            value={editForm.taskTitle || ''}
            onChange={e => onInputChange('taskTitle', e.target.value)}
            className="w-full rounded-none border border-primary bg-white p-3 text-base text-subText outline-none focus:border-primary focus:outline-none"
            placeholder="Enter task title"
          />
        ) : (
          <p className="break-all text-base text-subText">
            {taskLog.taskTitle}
          </p>
        )}
      </div>

      {/* Description */}
      <div className="rounded-lg border border-[#DFEAF2] p-4">
        <h3 className="mb-3 text-sm font-medium text-grayTen">Description</h3>
        {isEditing ? (
          <textarea
            value={editForm.description || ''}
            onChange={e => onInputChange('description', e.target.value)}
            rows={8}
            className="w-full resize-none rounded-none border border-primary bg-white p-3 text-subText outline-none focus:border-primary focus:outline-none focus:ring-0"
            placeholder="Enter task description"
          />
        ) : (
          <p className="break-all leading-relaxed text-subText">
            {taskLog.description}
          </p>
        )}
      </div>
    </div>
  );
};

export default LogBasicInfo;
