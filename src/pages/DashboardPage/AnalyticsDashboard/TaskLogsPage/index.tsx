import React, { useMemo } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import { Icons } from '@/assets/icons/DashboardIcons';
import Pagination from '@/components/common/Pagination';
import { TablePageContainer } from '@/components/layout/DashboardWithChatLayout';
import EmptyState from '@/components/ui/EmptyState';
import DataTable, { Column } from '@/components/ui/tables/DataTable';
import { ROUTES } from '@/constants/routes';
import { useTenant } from '@/context/TenantContext';
import { useTimezone } from '@/context/TimezoneContext';
import { useDebounce } from '@/hooks/useDebounce';
import { useMediaQuery } from '@/hooks/useMediaQuery';
import { usePagination } from '@/hooks/usePagination';
import { useTaskLogsList } from '@/hooks/useTaskLog';
import {
  mapPriorityToDisplay,
  mapStatusToDisplay,
  TaskLog,
  TaskLogPriority,
  TaskLogPriorityDisplay,
  TaskLogStatus,
  TaskLogStatusDisplay,
} from '@/types/taskLog';
import { useAnalyticsParams } from '@/utils/urlParams';

interface TaskLogTableRow {
  id: string;
  dateCreated: string;
  time: string;
  taskTitle: string;
  assignedBy: string;
  assignedTo: string;
  type: string;
  priority: TaskLogPriorityDisplay;
  status: TaskLogStatusDisplay;
  escalationPolicy: string;
  [key: string]:
    | string
    | TaskLogPriorityDisplay
    | TaskLogStatusDisplay
    | undefined;
}

const TaskLogsPage: React.FC = () => {
  const { filters } = useAnalyticsParams();
  const navigate = useNavigate();
  const location = useLocation();
  const { tenantId } = useTenant();
  const { formatUserTimestamp } = useTimezone();
  const isMobile = useMediaQuery('(max-width: 768px)');

  // Get search term from URL params
  const searchParams = new URLSearchParams(location.search);
  const searchTerm = searchParams.get('search') || '';

  // Debounce search term
  const debouncedSearchTerm = useDebounce(searchTerm, 700);

  // Pagination state
  const { page: currentPage, setPage: setCurrentPage } = usePagination();
  const pageSize = 10;

  // Build filter using URL params and tenant - get filters from URL params
  const urlParams = new URLSearchParams(location.search);
  const statusFilter = (urlParams.get('status') as TaskLogStatus) || undefined;
  const priorityFilter =
    (urlParams.get('priority') as TaskLogPriority) || undefined;
  const fromDate = urlParams.get('from') || undefined;
  const toDate = urlParams.get('to') || undefined;

  const taskLogFilter = {
    search: debouncedSearchTerm,
    createdBy: filters.agent || '',
    tenantId: tenantId || '',
    status: statusFilter,
    priority: priorityFilter,
    from: fromDate,
    to: toDate,
    page: currentPage,
    pageSize: pageSize,
  };

  // Use the hook to fetch task logs with filter - only when we have both required fields
  const { data: taskLogsResponse, isLoading } = useTaskLogsList(
    taskLogFilter,
    !!tenantId && !!taskLogFilter.createdBy && taskLogFilter.createdBy !== ''
  );

  const taskLogs = Array.isArray(taskLogsResponse)
    ? taskLogsResponse
    : taskLogsResponse?.data?.tasks || [];
  const totalCount = taskLogsResponse?.data?.total || taskLogs.length;
  const totalPages = Math.ceil(totalCount / pageSize);

  // Convert TaskLog data to table format
  const convertToTableData = (taskLogs: TaskLog[]): TaskLogTableRow[] => {
    return taskLogs.map(log => ({
      id: log.id,
      dateCreated: formatUserTimestamp(log.createdAt, 'date'),
      time: formatUserTimestamp(log.createdAt, 'time'),
      taskTitle: log.taskTitle,
      assignedTo: log.assignedTo || 'Unassigned',
      assignedBy: log.assignedBy || 'Unassigned',
      type: log?.taskTypeReference?.name || '--',
      priority: mapPriorityToDisplay(log.priority),
      status: mapStatusToDisplay(log.status),
      escalationPolicy: log.escalationPolicy || 'Default',
    }));
  };

  const tableData = convertToTableData(taskLogs);

  const allColumns = useMemo<Column<TaskLogTableRow>[]>(() => {
    const getStatusStyles = (status: string) => {
      switch (status) {
        case 'Completed':
          return 'bg-[#56965A] text-white';
        case 'Not Started':
          return 'bg-[#808894] text-white';
        case 'In Progress':
          return 'bg-[#F0A840] text-white';
        case 'Queued':
          return 'bg-[#3B82F6] text-white';
        case 'Failed':
          return 'bg-red-500 text-white';
        case 'Escalated':
          return 'bg-[#EA580C] text-white';
        default:
          return 'bg-gray-400 text-white';
      }
    };

    return [
      {
        key: 'dateCreated',
        label: 'timestamp',
        sortable: true,
        render: (value, row) => (
          <div className="flex flex-col items-start gap-1">
            <span>{value};</span>
            <span className="font-bold text-blackOne">{row.time}</span>
          </div>
        ),
      },
      {
        key: 'taskTitle',
        label: 'Task Title',
        sortable: true,
        wrap: isMobile,
        render: value => (
          <div
            className={
              isMobile
                ? 'line-clamp-2 max-w-24 text-sm leading-[18px] sm:max-w-xs'
                : 'max-w-xs truncate'
            }
            title={String(value)}
          >
            {String(value)}
          </div>
        ),
      },
      {
        key: 'assignedTo',
        label: 'Assigned To',
        sortable: true,
        render: value => (
          <div className="max-w-xs truncate capitalize" title={String(value)}>
            {String(value)}
          </div>
        ),
      },
      {
        key: 'type',
        label: 'Type',
        sortable: true,
      },
      {
        key: 'priority',
        label: 'Priority',
        sortable: true,
        render: value => {
          const priority = value as TaskLogTableRow['priority'];
          return (
            <span className={`px-3 py-1.5 text-xs font-medium`}>
              {priority}
            </span>
          );
        },
      },
      {
        key: 'status',
        label: 'Status',
        sortable: true,
        render: value => {
          const status = value as TaskLogTableRow['status'];

          return (
            <span
              className={`rounded-md px-3 py-1.5 text-xs font-medium ${getStatusStyles(
                status
              )}`}
            >
              {status}
            </span>
          );
        },
      },
    ];
  }, [isMobile]);

  const columns: Column<TaskLogTableRow>[] = useMemo(() => {
    if (!isMobile) {
      return allColumns;
    }

    const mobileKeys: Array<keyof TaskLogTableRow> = [
      'dateCreated',
      'taskTitle',
      'status',
    ];

    return allColumns.filter(column => mobileKeys.includes(column.key));
  }, [allColumns, isMobile]);

  const handleRowClick = (row: TaskLogTableRow) => {
    // Navigate to task log details page
    navigate(ROUTES.DASHBOARD_ANALYTICS_TASK_LOG_DETAILS(row.id));
  };

  // Show empty state when no data and not loading
  if (!isLoading && tableData.length === 0) {
    return (
      <div className="px-4 sm:px-6">
        <EmptyState
          icon={
            searchTerm ? (
              <Icons.SearchX className="h-6 w-6 text-white sm:h-8 sm:w-8" />
            ) : (
              <Icons.FileEmpty className="h-6 w-6 text-white sm:h-8 sm:w-8" />
            )
          }
          title={
            searchTerm
              ? 'No task logs found matching your search'
              : 'Task Log is Empty.'
          }
          description={
            searchTerm
              ? "Try adjusting your search terms or filters to find what you're looking for."
              : 'As your agents begin to work, this log will track every action, message, and outcome, all in one place.'
          }
        />
      </div>
    );
  }

  return (
    <>
      <TablePageContainer>
        <DataTable
          data={tableData}
          columns={columns}
          onRowClick={handleRowClick}
          loading={isLoading}
          emptyMessage={
            searchTerm
              ? 'No task logs found matching your search'
              : 'No task logs found'
          }
          rowColoring={true}
          rowColoringType="odd"
          showCheckAll={false}
        />
      </TablePageContainer>

      {/* Pagination */}
      {totalPages > 0 && !isLoading && (
        <div className="mt-6">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={setCurrentPage}
          />
        </div>
      )}
    </>
  );
};

export default TaskLogsPage;
