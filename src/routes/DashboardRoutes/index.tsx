import { lazy } from 'react';
import { Navigate } from 'react-router-dom';

import { withSuspense } from '@/components/hocs/suspense/withSuspense';
import { ROUTE_PATHS } from '@/constants/routes';
import { retryChunkLoad } from '@/utils/chunkErrorHandler';

// Lazy load dashboard pages with chunk error handling
const DashboardPage = withSuspense(
  lazy(() => retryChunkLoad(() => import('../../pages/DashboardPage')))
);

const DashboardActivatePage = withSuspense(
  lazy(() =>
    retryChunkLoad(
      () => import('../../pages/DashboardPage/DashboardActivatePage')
    )
  )
);

const AIAgentsDashboardPage = withSuspense(
  lazy(() => retryChunkLoad(() => import('../../pages/AiAgentsPage')))
);

const AgentSuiteDetailPage = withSuspense(
  lazy(() =>
    retryChunkLoad(
      () => import('../../pages/AiAgentsPage/AgentSuiteDetailPage')
    )
  )
);

const AgentActivationPage = withSuspense(
  lazy(() =>
    retryChunkLoad(() => import('../../pages/AiAgentsPage/AgentActivationPage'))
  )
);

const BusinessStackDashboardPage = withSuspense(
  lazy(() => retryChunkLoad(() => import('../../pages/BusinessStackPage')))
);

const BusinessStackSelectAgentPage = withSuspense(
  lazy(() =>
    retryChunkLoad(
      () => import('../../pages/BusinessStackPage/SelectAgentPage')
    )
  )
);

const BusinessStackActivateSuitePage = withSuspense(
  lazy(() =>
    retryChunkLoad(
      () => import('../../pages/BusinessStackPage/ActivateSuitePage')
    )
  )
);

const SalesforceHelpPage = withSuspense(
  lazy(() =>
    retryChunkLoad(
      () => import('../../pages/BusinessStackPage/SalesforceHelpPage')
    )
  )
);

const KnowledgeBaseDashboardPage = withSuspense(
  lazy(() => retryChunkLoad(() => import('../../pages/KnowledgeBasePage')))
);

const KnowledgeBaseSelectAgentPage = withSuspense(
  lazy(() =>
    retryChunkLoad(
      () => import('../../pages/KnowledgeBasePage/SelectAgentPage')
    )
  )
);

const KnowledgeBaseActivationPage = withSuspense(
  lazy(() =>
    retryChunkLoad(
      () => import('../../pages/KnowledgeBasePage/KnowledgeBaseActivationPage')
    )
  )
);

const SettingsPage = withSuspense(
  lazy(() => retryChunkLoad(() => import('../../pages/SettingsPage')))
);

const MembersSettings = withSuspense(
  lazy(() =>
    retryChunkLoad(() => import('../../pages/SettingsPage/MembersSettings'))
  )
);

const InviteMembersPage = withSuspense(
  lazy(() =>
    retryChunkLoad(
      () => import('../../pages/SettingsPage/MembersSettings/InviteMembersPage')
    )
  )
);

const RequestToJoinPage = withSuspense(
  lazy(() =>
    retryChunkLoad(
      () => import('../../pages/SettingsPage/MembersSettings/RequestToJoinPage')
    )
  )
);

// New Analytics Dashboard Components
const AnalyticsDashboard = withSuspense(
  lazy(() =>
    retryChunkLoad(() => import('../../pages/DashboardPage/AnalyticsDashboard'))
  )
);

const InsightsPage = withSuspense(
  lazy(() =>
    retryChunkLoad(
      () => import('../../pages/DashboardPage/AnalyticsDashboard/InsightsPage')
    )
  )
);

const TaskLogsPage = withSuspense(
  lazy(() =>
    retryChunkLoad(
      () => import('../../pages/DashboardPage/AnalyticsDashboard/TaskLogsPage')
    )
  )
);

const AssignmentLogsPage = withSuspense(
  lazy(() =>
    retryChunkLoad(
      () =>
        import(
          '../../pages/DashboardPage/AnalyticsDashboard/AssignmentLogsPage'
        )
    )
  )
);

const AssignmentLogDetailsPage = withSuspense(
  lazy(() =>
    retryChunkLoad(
      () =>
        import(
          '../../pages/DashboardPage/AnalyticsDashboard/AssignmentLogsPage/AssignmentLogDetailsPage'
        )
    )
  )
);

const AllInsightsPage = withSuspense(
  lazy(() =>
    retryChunkLoad(
      () =>
        import(
          '../../pages/DashboardPage/AnalyticsDashboard/InsightsPage/AllInsightsPage'
        )
    )
  )
);

const TaskLogDetailsPage = withSuspense(
  lazy(() =>
    retryChunkLoad(
      () =>
        import(
          '../../pages/DashboardPage/AnalyticsDashboard/TaskLogsPage/TaskLogDetailsPage'
        )
    )
  )
);

const AnalyticsSuiteDetailsPage = withSuspense(
  lazy(() =>
    retryChunkLoad(
      () =>
        import('../../pages/DashboardPage/AnalyticsDashboard/SuiteDetailsPage')
    )
  )
);

const NotFoundPage = withSuspense(
  lazy(() => retryChunkLoad(() => import('../../pages/NotFoundPage')))
);

export const DashboardRoutes = [
  {
    index: true,
    element: <Navigate to="ai-agents" replace />,
  },
  {
    path: ROUTE_PATHS.DASHBOARD_ANALYTICS_INSIGHTS,
    element: <AnalyticsDashboard />,
    children: [
      {
        index: true,
        element: <InsightsPage />,
      },
      {
        path: 'all',
        element: <AllInsightsPage />,
      },
    ],
  },
  {
    path: ROUTE_PATHS.DASHBOARD_ANALYTICS_TASK_LOGS,
    element: <AnalyticsDashboard />,
    children: [
      {
        index: true,
        element: <TaskLogsPage />,
      },
      {
        path: ':taskId',
        element: <TaskLogDetailsPage />,
      },
    ],
  },
  {
    path: ROUTE_PATHS.DASHBOARD_ANALYTICS_ASSIGNMENT_LOGS,
    element: <AnalyticsDashboard />,
    children: [
      {
        index: true,
        element: <AssignmentLogsPage />,
      },
      {
        path: ':id',
        element: <AssignmentLogDetailsPage />,
      },
    ],
  },
  {
    path: 'analytics/select-agent',
    element: <DashboardPage />,
  },
  {
    path: 'analytics/activate-suite/:suiteId',
    element: <DashboardActivatePage />,
  },
  {
    path: 'analytics/suite/:suiteId',
    element: <AnalyticsSuiteDetailsPage />,
  },
  {
    path: 'ai-agents',
    element: <AIAgentsDashboardPage />,
  },
  {
    path: 'ai-agents/suite/:suiteId',
    element: <AgentSuiteDetailPage />,
  },
  {
    path: 'ai-agents/activate',
    element: <AgentActivationPage />,
  },
  {
    path: 'ai-agents/activate/suite/:suiteId',
    element: <AgentActivationPage />,
  },
  {
    path: 'ai-agents/activate/agent/:agentId',
    element: <AgentActivationPage />,
  },
  {
    path: 'business-stack',
    element: <BusinessStackDashboardPage />,
  },
  {
    path: 'business-stack/select-agent',
    element: <BusinessStackSelectAgentPage />,
  },
  {
    path: 'business-stack/activate-suite/:suiteId',
    element: <BusinessStackActivateSuitePage />,
  },
  {
    path: 'business-stack/salesforce-help',
    element: <SalesforceHelpPage />,
  },
  {
    path: 'business-stack/:appKey',
    element: <BusinessStackDashboardPage />,
  },
  {
    path: 'knowledge-base',
    element: <KnowledgeBaseDashboardPage />,
  },
  {
    path: 'knowledge-base/select-agent',
    element: <KnowledgeBaseSelectAgentPage />,
  },
  {
    path: 'knowledge-base/activate-suite/:suiteId',
    element: <KnowledgeBaseActivationPage />,
  },
  {
    path: 'settings/*',
    element: <SettingsPage />,
  },
  {
    path: 'members',
    element: <MembersSettings />,
  },
  {
    path: 'members/invite',
    element: <InviteMembersPage />,
  },
  {
    path: 'members/request-to-join',
    element: <RequestToJoinPage />,
  },
  {
    path: '*',
    element: <NotFoundPage />,
  },
];
