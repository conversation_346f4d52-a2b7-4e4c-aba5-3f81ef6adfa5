import { useCallback, useEffect, useRef, useState } from 'react';

import { useTenant } from '@/context/TenantContext';

import { useAuth } from '../context/AuthContext';
import { generateSecureSessionId } from '../services/businessStackService';
import {
  filterRequestFieldFromMessage,
  useScyraChatApi,
  useScyraChatHistoryApi,
} from '../services/scyraChatService';
import { ChatHistoryItem, MessageSender } from '../types/agents';
import { ScyraChatState, ScyraMessage } from '../types/businessStack';
import { extractErrorMessage } from '../utils/errorUtils';
import { useConversationState } from './useConversationState';

export const useScyraChat = (
  externalMessage?: string,
  externalInjectedMessage?: string
) => {
  const { user } = useAuth();
  const {
    isLoading: isActiveTenantLoading,
    activeAgent,
    waitForAgentReady,
  } = useTenant();
  const [state, setState] = useState<ScyraChatState>({
    messages: [],
    isLoading: false,
    sessionId: generateSecureSessionId(),
  });

  const chatWithScyra = useScyraChatApi();
  const fetchChatHistory = useScyraChatHistoryApi();

  const getWelcomeMessage = useCallback((): string => {
    if (user?.firstName) {
      return `Hi ${user.firstName}, What would you like to do today?`;
    }
    return `Hi, What would you like to do today?`;
  }, [user?.firstName]);

  const capitalizeAgentName = useCallback((key: string) => {
    if (!key) return 'Agent';
    return key.charAt(0).toUpperCase() + key.slice(1);
  }, []);

  // Function to add external error messages to chat
  const addInjectedMessage = useCallback(
    (injectedMessage: string) => {
      const injectedMsg: ScyraMessage = {
        id: `error-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
        sender: activeAgent || '',
        content: injectedMessage,
        timestamp: new Date(),
        senderName: capitalizeAgentName(activeAgent || ''),
      };

      setState(prev => ({
        ...prev,
        messages: [...prev.messages, injectedMsg],
      }));
    },
    [activeAgent]
  );

  // Handle external injected messages
  useEffect(() => {
    if (externalInjectedMessage && externalInjectedMessage.trim()) {
      addInjectedMessage(externalInjectedMessage);
    }
  }, [externalInjectedMessage, addInjectedMessage]);

  // Helper to map API history to ScyraMessage
  const mapHistoryToScyraMessage = useCallback(
    (item: ChatHistoryItem, index: number): ScyraMessage => {
      const senderKey = item.sender === activeAgent ? activeAgent : 'user';
      return {
        id: `${senderKey}-${item.createdAt}-${index}`,
        sender: senderKey,
        // content: filterRequestFieldFromMessage(item.message),
        content: item.message,
        timestamp: new Date(item.createdAt),
        senderName:
          senderKey === 'user' ? 'You' : capitalizeAgentName(activeAgent || ''),
      };
    },
    [activeAgent, capitalizeAgentName]
  );

  // Consolidated chat history loading function
  const loadChatHistory = useCallback(async () => {
    if (isActiveTenantLoading) {
      // console.log('Skipping chat history load - tenant/agent still loading');
      return;
    }

    // Set a timeout to ensure loading state is cleared even if something goes wrong
    const loadingTimeoutId = setTimeout(() => {
      console.warn('Chat history loading timeout - clearing loading state');
      setState(prev => ({ ...prev, isLoading: false }));
    }, 10000); // 10 second timeout

    try {
      setState(prev => ({ ...prev, isLoading: true }));
      const history = await fetchChatHistory();
      let messages: ScyraMessage[] = [];

      if (history && history.length > 0) {
        messages = history
          .sort(
            (a, b) =>
              new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
          )
          .map(mapHistoryToScyraMessage);
      }

      // Always add welcome message if no history
      if (messages.length === 0) {
        messages.push({
          id: 'welcome-message',
          sender: activeAgent || '',
          content: getWelcomeMessage(),
          timestamp: new Date(),
          senderName: capitalizeAgentName(activeAgent || ''),
        });
      }

      // Clear timeout and reset session id when switching agents
      clearTimeout(loadingTimeoutId);
      setState(prev => ({
        ...prev,
        messages,
        isLoading: false,
        sessionId: generateSecureSessionId(),
      }));
    } catch (error) {
      console.error('Failed to load chat history:', error);
      // Clear timeout and fallback to welcome message
      clearTimeout(loadingTimeoutId);
      setState(prev => ({
        ...prev,
        isLoading: false,
        messages: [
          {
            id: 'welcome-message',
            sender: activeAgent || '',
            content: getWelcomeMessage(),
            timestamp: new Date(),
            senderName: capitalizeAgentName(activeAgent || ''),
          },
        ],
      }));
    }
  }, [
    isActiveTenantLoading,
    fetchChatHistory,
    mapHistoryToScyraMessage,
    getWelcomeMessage,
    activeAgent,
    capitalizeAgentName,
  ]);

  // Load chat history when tenant/agent is ready or when activeAgent changes
  useEffect(() => {
    loadChatHistory();
  }, [loadChatHistory]);

  // Safety mechanism: Clear loading state when agent changes to prevent stuck TypingIndicator
  useEffect(() => {
    setState(prev => {
      if (prev.isLoading) {
        // console.log(
        //   'Agent changed - clearing loading state to prevent stuck TypingIndicator'
        // );
        return { ...prev, isLoading: false };
      }
      return prev;
    });
  }, [activeAgent]);

  const sendMessage = useCallback(
    async (messageContent: string) => {
      if (!messageContent.trim() || state.isLoading) return;

      // Prevent sending while tenant/agent data is still loading
      if (isActiveTenantLoading) {
        // Add a local message informing user to wait
        setState(
          prev =>
            ({
              ...prev,
              messages: [
                ...prev.messages,
                {
                  id: `error-${Date.now()}`,
                  sender: activeAgent || '',
                  content:
                    'Agent is still initializing. Please wait a moment and try again.',
                  timestamp: new Date(),
                  senderName: capitalizeAgentName(activeAgent || ''),
                },
              ],
            }) as ScyraChatState
        );
        return;
      }

      // Add user message
      const userMessage: ScyraMessage = {
        id: `user-${Date.now()}`,
        sender: 'user',
        content: messageContent.trim(),
        timestamp: new Date(),
        senderName: 'You',
      };

      setState(prev => ({
        ...prev,
        messages: [...prev.messages, userMessage],
        isLoading: true,
      }));

      try {
        // Call Scyra API
        const response = await chatWithScyra({
          userMessage: messageContent.trim(),
          sessionId: state.sessionId,
        });

        // Add agent response (with additional filtering for safety)
        const agentMessage: ScyraMessage = {
          id: `${activeAgent}-${Date.now()}`,
          sender: activeAgent || '',
          content: filterRequestFieldFromMessage(response),
          timestamp: new Date(),
          senderName: capitalizeAgentName(activeAgent || ''),
        };

        setState(prev => ({
          ...prev,
          messages: [...prev.messages, agentMessage],
          isLoading: false,
        }));
      } catch (error) {
        console.error(`Error sending message to ${activeAgent}:`, error);

        // Add error message
        const errorMessage: ScyraMessage = {
          id: `error-${Date.now()}`,
          sender: activeAgent || '',
          content: extractErrorMessage(error),
          timestamp: new Date(),
          senderName: capitalizeAgentName(activeAgent || ''),
        };

        setState(prev => ({
          ...prev,
          messages: [...prev.messages, errorMessage],
          isLoading: false,
        }));
      }
    },
    [chatWithScyra, state.sessionId, state.isLoading]
  );

  // Track auto-send to prevent duplicates
  const hasAutoSentExternalMessage = useRef(false);

  // Handle external messages (from HeroSection)
  const handleExternalMessage = useCallback(async () => {
    if (
      externalMessage &&
      externalMessage.trim() &&
      !hasAutoSentExternalMessage.current
    ) {
      // Double-check that we're targeting the correct agent
      if (activeAgent !== 'regis') {
        console.warn(
          `Agent mismatch before auto-send: expected regis, current ${activeAgent}. Waiting...`
        );
        return; // Don't send yet, wait for agent to be correct
      }

      // Wait for agent to be fully ready
      const isReady = await waitForAgentReady('regis');
      if (!isReady) {
        console.error('Agent not ready for auto-send, skipping message');
        return;
      }

      // Final check after waiting
      if (activeAgent === 'regis') {
        hasAutoSentExternalMessage.current = true;
        sendMessage(externalMessage);
      } else {
        console.error(
          `Agent still mismatched after waiting: expected regis, got ${activeAgent}`
        );
      }
    }
  }, [externalMessage, sendMessage, activeAgent, waitForAgentReady]);

  useEffect(() => {
    handleExternalMessage();
    if (externalMessage && externalMessage.trim()) {
      hasAutoSentExternalMessage.current = true;
    }
  }, [handleExternalMessage, externalMessage]);

  // Group messages by date (YYYY-MM-DD)
  const groupMessagesByDate = () => {
    const grouped: Record<string, ScyraMessage[]> = {};
    const sorted = [...state.messages].sort(
      (a, b) => a.timestamp.getTime() - b.timestamp.getTime()
    );
    sorted.forEach(msg => {
      const dateKey = msg.timestamp.toISOString().split('T')[0]; // YYYY-MM-DD format
      if (!grouped[dateKey]) grouped[dateKey] = [];
      grouped[dateKey].push(msg);
    });
    return grouped;
  };

  // Expose the consolidated loadChatHistory as reloadChatHistory for external use
  const reloadChatHistory = useCallback(async () => {
    await loadChatHistory();
  }, [loadChatHistory]);

  return {
    state,
    sendMessage,
    groupMessagesByDate,
    reloadChatHistory,
    addInjectedMessage,
  };
};

export const useScyraChatHistory = () => {
  const fetchChatHistory = useScyraChatHistoryApi();
  const { addMessage } = useConversationState();
  const { activeAgent } = useTenant();

  // Converts API history item to ChatMessage format
  const mapHistoryToChatMessage = (item: ChatHistoryItem) => {
    const sender: MessageSender =
      item.sender === activeAgent ? activeAgent : 'user';
    return {
      id: `${sender}-${item.createdAt}`,
      sender,
      content: filterRequestFieldFromMessage(item.message),
      timestamp: new Date(item.createdAt),
      senderName: sender === 'user' ? 'You' : activeAgent,
    };
  };

  // Loads and adds chat history to state, sorted by date ascending
  const loadChatHistory = useCallback(async () => {
    const history = await fetchChatHistory();
    // Sort by createdAt ascending
    const sorted = history.sort(
      (a, b) =>
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
    );
    // Add each message to state
    sorted.forEach(item => {
      if (!activeAgent) return;
      const msg = mapHistoryToChatMessage(item);
      addMessage(msg.sender, msg.content, msg?.senderName || '');
    });
  }, [fetchChatHistory, addMessage]);

  return { loadChatHistory };
};
