/**
 * Geolocation service for detecting user timezone using IP-based geolocation
 * Used as a fallback when browser timezone detection is unavailable
 */

export interface GeolocationResponse {
  timezone: string;
  country: string;
  city: string;
  ip: string;
}

export interface GeolocationError {
  error: string;
  message: string;
}

/**
 * Detect user timezone using IP-based geolocation
 * Uses ipapi.co as the primary service with fallback to worldtimeapi.org
 */
export class GeolocationService {
  private static readonly IPAPI_URL = 'https://ipapi.co/json/';
  private static readonly WORLDTIME_API_URL = 'https://worldtimeapi.org/api/ip';
  private static readonly REQUEST_TIMEOUT = 5000; // 5 seconds

  /**
   * Get user timezone using IP-based geolocation
   * @returns Promise<string> - IANA timezone identifier (e.g., 'America/New_York')
   */
  static async detectTimezone(): Promise<string> {
    try {
      // Try primary service first (ipapi.co)
      const timezone = await this.tryIpApiCo();
      if (timezone) {
        console.log(`Timezone detected via ipapi.co: ${timezone}`);
        return timezone;
      }
    } catch (error) {
      console.warn('ipapi.co failed, trying fallback service:', error);
    }

    try {
      // Try fallback service (worldtimeapi.org)
      const timezone = await this.tryWorldTimeApi();
      if (timezone) {
        console.log(`Timezone detected via worldtimeapi.org: ${timezone}`);
        return timezone;
      }
    } catch (error) {
      console.warn('worldtimeapi.org failed:', error);
    }

    // If both services fail, throw error
    throw new Error('Unable to detect timezone via IP geolocation');
  }

  /**
   * Try to get timezone from ipapi.co
   */
  private static async tryIpApiCo(): Promise<string | null> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.REQUEST_TIMEOUT);

    try {
      const response = await fetch(this.IPAPI_URL, {
        signal: controller.signal,
        headers: {
          'Accept': 'application/json',
        },
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data: GeolocationResponse | GeolocationError = await response.json();

      // Check if response contains error
      if ('error' in data) {
        throw new Error(data.message || data.error);
      }

      // Validate timezone
      if (data.timezone && this.isValidTimezone(data.timezone)) {
        return data.timezone;
      }

      return null;
    } catch (error) {
      clearTimeout(timeoutId);
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Request timeout');
      }
      throw error;
    }
  }

  /**
   * Try to get timezone from worldtimeapi.org
   */
  private static async tryWorldTimeApi(): Promise<string | null> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.REQUEST_TIMEOUT);

    try {
      const response = await fetch(this.WORLDTIME_API_URL, {
        signal: controller.signal,
        headers: {
          'Accept': 'application/json',
        },
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data: { timezone?: string } = await response.json();

      // Validate timezone
      if (data.timezone && this.isValidTimezone(data.timezone)) {
        return data.timezone;
      }

      return null;
    } catch (error) {
      clearTimeout(timeoutId);
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Request timeout');
      }
      throw error;
    }
  }

  /**
   * Validate if a timezone string is a valid IANA timezone identifier
   */
  private static isValidTimezone(timezone: string): boolean {
    try {
      Intl.DateTimeFormat(undefined, { timeZone: timezone });
      return true;
    } catch {
      return false;
    }
  }
}

/**
 * Convenience function to detect timezone with error handling
 * @returns Promise<string | null> - Returns timezone or null if detection fails
 */
export const detectTimezoneViaIP = async (): Promise<string | null> => {
  try {
    return await GeolocationService.detectTimezone();
  } catch (error) {
    console.warn('IP-based timezone detection failed:', error);
    return null;
  }
};
